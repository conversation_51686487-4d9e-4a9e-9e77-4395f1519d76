#!/bin/bash

# No-Time-to-Train Web应用启动脚本

set -e

echo "🚀 启动 No-Time-to-Train Web应用"
echo "================================="

# 检查当前目录
if [ ! -f "README.md" ] || [ ! -d "sam2" ]; then
    echo "❌ 错误: 请在nottrain项目根目录运行此脚本"
    exit 1
fi

# 激活conda环境
echo "🔧 激活conda环境..."
if command -v conda >/dev/null 2>&1; then
    source ~/.bashrc
    conda activate notrain
    echo "✅ 已激活 notrain 环境"
else
    echo "⚠️  未检测到conda，请确保已安装PyTorch和其他依赖"
fi

# 检查Python和torch
echo "🔍 检查依赖..."
python -c "import torch; print(f'✅ PyTorch版本: {torch.__version__}')" || {
    echo "❌ PyTorch未安装或版本不兼容"
    exit 1
}

python -c "import torch; print(f'✅ CUDA可用: {torch.cuda.is_available()}')"
if python -c "import torch; exit(0 if torch.cuda.is_available() else 1)"; then
    python -c "import torch; print(f'✅ GPU设备: {torch.cuda.get_device_name(0)}')"
fi

# 安装Web应用依赖
echo "📦 安装Web应用依赖..."
if [ -f "app/requirements.txt" ]; then
    pip install -r app/requirements.txt
    echo "✅ 依赖安装完成"
else
    echo "⚠️  未找到requirements.txt，跳过依赖安装"
fi

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p app/uploads
mkdir -p app/results
mkdir -p app/static
mkdir -p app/templates
echo "✅ 目录创建完成"

# 检查关键文件
echo "🔍 检查关键文件..."
required_files=(
    "app/main.py"
    "app/nottrain_wrapper.py" 
    "app/utils.py"
    "app/templates/index.html"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ 缺少文件: $file"
        exit 1
    fi
done

# 检查SAM2和DinoV2检查点
echo "🔍 检查模型检查点..."
if [ -f "checkpoints/sam2_hiera_large.pt" ]; then
    echo "✅ SAM2检查点存在"
else
    echo "⚠️  SAM2检查点不存在，请下载："
    echo "   cd checkpoints && wget https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt"
fi

if [ -f "checkpoints/dinov2/dinov2_vitl14_pretrain.pth" ]; then
    echo "✅ DinoV2检查点存在"
else
    echo "⚠️  DinoV2检查点不存在，请下载："
    echo "   cd checkpoints/dinov2 && wget https://dl.fbaipublicfiles.com/dinov2/dinov2_vitl14/dinov2_vitl14_pretrain.pth"
fi

# 设置环境变量
echo "⚙️  设置环境变量..."
export FLASK_APP=app/main.py
export FLASK_ENV=development
export PYTHONPATH="${PYTHONPATH}:.:./sam2:./dinov2"

# 如果有GPU，设置CUDA相关环境变量
if python -c "import torch; exit(0 if torch.cuda.is_available() else 1)" 2>/dev/null; then
    export CUDA_VISIBLE_DEVICES=0
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    echo "✅ GPU环境配置完成"
fi

echo ""
echo "🌐 启动Web服务器..."
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动Flask应用
cd "$(dirname "$0")"
python app/main.py

echo ""
echo "👋 Web应用已停止" 