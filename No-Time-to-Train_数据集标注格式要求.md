# No-Time-to-Train 数据集标注格式要求

## 📋 概述

**No-Time-to-Train** 项目对数据集的标注格式有严格要求。项目需要的是**COCO格式的实例分割标注**，而不是简单的目标检测边界框。

## 🔍 关键要求

### ⚠️ 最重要的要求
**必须包含分割轮廓（segmentation）信息**，仅有边界框（bbox）是不够的！

## 📁 目录结构

```
your_dataset/
├── images/                     # 图像文件夹
│   ├── image_001.jpg
│   ├── image_002.jpg
│   └── ...
└── annotations/               # 标注文件夹
    └── instances.json         # COCO格式标注文件
```

## 📊 COCO格式标注文件结构

### 完整的标注文件结构

```json
{
    "images": [
        {
            "id": 1,
            "file_name": "image_001.jpg",
            "width": 640,
            "height": 480
        }
    ],
    "annotations": [
        {
            "id": 1,
            "image_id": 1,
            "category_id": 1,
            "bbox": [x, y, width, height],
            "segmentation": [[x1,y1,x2,y2,x3,y3,...]],
            "area": 1234,
            "iscrowd": 0,
            "ignore": 0
        }
    ],
    "categories": [
        {
            "id": 1,
            "name": "category_name",
            "supercategory": "supercategory_name"
        }
    ]
}
```

### 各字段详细说明

#### 1. `images` 数组
每个图像对象包含：
- `id`: 图像的唯一标识符（整数）
- `file_name`: 图像文件名（字符串）
- `width`: 图像宽度（像素）
- `height`: 图像高度（像素）

#### 2. `annotations` 数组
每个标注对象**必须**包含：

**必填字段：**
- `id`: 标注的唯一标识符
- `image_id`: 对应的图像ID
- `category_id`: 对应的类别ID
- `bbox`: 边界框 `[x, y, width, height]` 格式
- `segmentation`: **🔥 关键字段！** 分割轮廓信息
- `area`: 分割区域的面积（像素数量）
- `iscrowd`: 是否为密集标注（通常为0）

**可选字段：**
- `ignore`: 是否忽略该标注（通常为0）

#### 3. `categories` 数组
每个类别对象包含：
- `id`: 类别的唯一标识符
- `name`: 类别名称
- `supercategory`: 父类别名称（可为空字符串）

## 🎯 分割标注格式详解

### `segmentation` 字段的两种格式

#### 1. 多边形格式（Polygon）- 推荐
```json
"segmentation": [
    [x1, y1, x2, y2, x3, y3, ..., xn, yn]
]
```

- 每个多边形是一个浮点数数组
- 数组中的数字成对出现，表示 `(x, y)` 坐标
- 支持多个分离的多边形（如果对象有多个部分）

**示例：**
```json
"segmentation": [
    [100.0, 100.0, 150.0, 100.0, 150.0, 150.0, 100.0, 150.0]
]
```
这表示一个矩形：(100,100) → (150,100) → (150,150) → (100,150)

#### 2. RLE格式（Run-Length Encoding）
```json
"segmentation": {
    "size": [height, width],
    "counts": "compressed_rle_string"
}
```

- 用于复杂形状或大型对象
- 更紧凑的存储格式

## ✅ 正确格式示例

### 用户当前数据格式（正确✅）
你的 `mydata/val_annotations.json` 文件格式是正确的：

```json
{
    "annotations": [
        {
            "segmentation": [
                [
                    578.390034, 80.876713,
                    580.266092, 80.01084,
                    584.306831, 79.433592,
                    // ... 更多坐标点
                ]
            ],
            "area": 89.76,
            "iscrowd": 0,
            "image_id": 1,
            "bbox": [578.390034, 79.433592, 13.565339, 8.225789],
            "category_id": 0,
            "id": 1,
            "ignore": 0
        }
    ]
}
```

**✅ 你的数据包含了所有必需字段，格式完全正确！**

### 不正确的格式示例

#### ❌ 错误1：缺少分割信息
```json
{
    "id": 1,
    "image_id": 1,
    "category_id": 1,
    "bbox": [100, 100, 50, 50],
    "area": 2500,
    "iscrowd": 0
    // ❌ 缺少 "segmentation" 字段！
}
```

#### ❌ 错误2：空的分割信息
```json
{
    "id": 1,
    "image_id": 1,
    "category_id": 1,
    "bbox": [100, 100, 50, 50],
    "segmentation": [],  // ❌ 空数组！
    "area": 2500,
    "iscrowd": 0
}
```

## 🛠️ 如何从边界框生成分割标注

如果你只有边界框标注，可以使用项目提供的工具转换：

```bash
# 使用SAM2从边界框生成分割标注
python scripts/bbox_to_segmentation.py \
    --input_json path/to/bbox_annotations.json \
    --output_json path/to/segmentation_annotations.json \
    --images_dir path/to/images/
```

## 📈 数据质量要求

### 1. 分割精度
- 分割轮廓应该紧贴对象边界
- 避免包含背景区域
- 对于有孔洞的对象，使用多个多边形表示

### 2. 标注完整性
- 每个可见对象都应该有标注
- 类别ID必须与categories中定义的一致
- 图像ID必须与images中定义的一致

### 3. 坐标精度
- 坐标可以是浮点数（推荐）
- 坐标应在图像范围内：0 ≤ x ≤ width, 0 ≤ y ≤ height

## 🔧 验证工具

项目中包含验证脚本来检查数据格式：

```bash
# 验证数据格式
python dev_hongyi/dataset/validate_annotations.py \
    --json_file path/to/annotations.json \
    --images_dir path/to/images/
```

## 📝 总结

**你的数据集 `mydata/val_annotations.json` 格式是完全正确的！**

包含了：
- ✅ 完整的图像信息
- ✅ 详细的分割轮廓（polygon格式）
- ✅ 边界框信息
- ✅ 面积计算
- ✅ 类别定义
- ✅ 所有必需字段

你的数据可以直接用于No-Time-to-Train项目，无需任何格式转换。

## 🚀 下一步

现在你可以：
1. 生成few-shot参考集
2. 构建记忆库
3. 运行推理流程

具体使用步骤请参考 `docs/no_time_to_train_usage_guide.md` 文档。 