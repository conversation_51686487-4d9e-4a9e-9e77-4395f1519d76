Metadata-Version: 2.4
Name: SAM-2
Version: 1.0
Summary: SAM 2: Segment Anything in Images and Videos
Home-page: https://github.com/facebookresearch/segment-anything-2
Author: <PERSON>a AI
Author-email: <EMAIL>
License: Apache 2.0
Requires-Python: >=3.10.0
Description-Content-Type: text/markdown
Requires-Dist: torch>=2.7.1
Requires-Dist: torchvision>=0.18.1
Requires-Dist: numpy>=1.24.4
Requires-Dist: tqdm>=4.66.1
Requires-Dist: hydra-core>=1.3.2
Requires-Dist: iopath>=0.1.10
Requires-Dist: pillow>=9.4.0
Provides-Extra: demo
Requires-Dist: matplotlib>=3.9.1; extra == "demo"
Requires-Dist: jupyter>=1.0.0; extra == "demo"
Requires-Dist: opencv-python>=4.7.0; extra == "demo"
Provides-Extra: dev
Requires-Dist: black==24.2.0; extra == "dev"
Requires-Dist: usort==1.0.2; extra == "dev"
Requires-Dist: ufmt==2.0.0b2; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

<div align="center">

# 🚀 No Time to Train!  
### Training-Free Reference-Based Instance Segmentation  
[![GitHub](https://img.shields.io/badge/%E2%80%8B-No%20Time%20To%20Train-black?logo=github)](https://github.com/miquel-espinosa/no-time-to-train)
[![Website](https://img.shields.io/badge/🌐-Project%20Page-grey)](https://miquel-espinosa.github.io/no-time-to-train/)
[![arXiv](https://img.shields.io/badge/arXiv-2507.02798-b31b1b)](https://arxiv.org/abs/2507.02798)

**State-of-the-art (Papers with Code)**

[**_1-shot_**](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-1-shot?p=no-time-to-train-training-free-reference) | [![PWC](https://img.shields.io/endpoint.svg?url=https://paperswithcode.com/badge/no-time-to-train-training-free-reference/few-shot-object-detection-on-ms-coco-1-shot)](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-1-shot?p=no-time-to-train-training-free-reference)

[**_10-shot_**](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-10-shot?p=no-time-to-train-training-free-reference) | [![PWC](https://img.shields.io/endpoint.svg?url=https://paperswithcode.com/badge/no-time-to-train-training-free-reference/few-shot-object-detection-on-ms-coco-10-shot)](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-10-shot?p=no-time-to-train-training-free-reference)

[**_30-shot_**](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-30-shot?p=no-time-to-train-training-free-reference) | [![PWC](https://img.shields.io/endpoint.svg?url=https://paperswithcode.com/badge/no-time-to-train-training-free-reference/few-shot-object-detection-on-ms-coco-30-shot)](https://paperswithcode.com/sota/few-shot-object-detection-on-ms-coco-30-shot?p=no-time-to-train-training-free-reference)

</div>

---

> 🔔 **Update (July 2025):** Code has been updated with instructions!

---

## 📋 Table of Contents

- [🎯 Highlights](#-highlights)
- [📜 Abstract](#-abstract)
- [🧠 Architecture](#-architecture)
- [🛠️ Installation instructions](#️-installation-instructions)
  - [1. Clone the repository](#1-clone-the-repository)
  - [2. Create conda environment](#2-create-conda-environment)
  - [3. Install SAM2 and DinoV2](#3-install-sam2-and-dinov2)
  - [4. Download datasets](#4-download-datasets)
  - [5. Download SAM2 and DinoV2 checkpoints](#5-download-sam2-and-dinov2-checkpoints)
- [📊 Inference code: Reproduce 30-shot SOTA results in Few-shot COCO](#-inference-code)
  - [0. Create reference set](#0-create-reference-set)
  - [1. Fill memory with references](#1-fill-memory-with-references)
  - [2. Post-process memory bank](#2-post-process-memory-bank)
  - [3. Inference on target images](#3-inference-on-target-images)
  - [Results](#results)
- [🔍 Citation](#-citation)


## 🎯 Highlights
- 💡 **Training-Free**: No fine-tuning, no prompt engineering—just a reference image.  
- 🖼️ **Reference-Based**: Segment new objects using just a few examples.  
- 🔥 **SOTA Performance**: Outperforms previous training-free approaches on COCO, PASCAL VOC, and Cross-Domain FSOD.

**Links:**
- 🧾 [**arXiv Paper**](https://arxiv.org/abs/2507.02798)  
- 🌐 [**Project Website**](https://miquel-espinosa.github.io/no-time-to-train/)  
- 📈 [**Papers with Code**](https://paperswithcode.com/paper/no-time-to-train-training-free-reference)

## 📜 Abstract

> The performance of image segmentation models has historically been constrained by the high cost of collecting large-scale annotated data. The Segment Anything Model (SAM) alleviates this original problem through a promptable, semantics-agnostic, segmentation paradigm and yet still requires manual visual-prompts or complex domain-dependent prompt-generation rules to process a new image. Towards reducing this new burden, our work investigates the task of object segmentation when provided with, alternatively, only a small set of reference images. Our key insight is to leverage strong semantic priors, as learned by foundation models, to identify corresponding regions between a reference and a target image. We find that correspondences enable automatic generation of instance-level segmentation masks for downstream tasks and instantiate our ideas via a multi-stage, training-free method incorporating (1) memory bank construction; (2) representation aggregation and (3) semantic-aware feature matching. Our experiments show significant improvements on segmentation metrics, leading to state-of-the-art performance on COCO FSOD (36.8% nAP), PASCAL VOC Few-Shot (71.2% nAP50) and outperforming existing training-free approaches on the Cross-Domain FSOD benchmark (22.4% nAP).

![cdfsod-results-final-comic-sans-min](https://github.com/user-attachments/assets/ab302c02-c080-4042-99fc-0e181ba8abb9)


## 🧠 Architecture

![training-free-architecture-comic-sans-min](https://github.com/user-attachments/assets/d84dd83a-505e-45a0-8ce3-98e1838017f9)


## 🛠️ Installation instructions

### 1. Clone the repository

```bash
git clone https://github.com/miquel-espinosa/no-time-to-train.git
cd no-time-to-train
```

### 2. Create conda environment

We will create a conda environment with the required packages.
```bash
conda env create -f environment.yml
conda activate no-time-to-train
```

### 3. Install SAM2 and DinoV2

We will install SAM2 and DinoV2 from source.
```bash
pip install -e .
cd dinov2
pip install -e .
cd ..
```

### 4. Download datasets

Please download COCO dataset and place it in `data/coco`

### 5. Download SAM2 and DinoV2 checkpoints

We will download the exact SAM2 checkpoints used in the paper.
(Note, however, that SAM2.1 checkpoints are already available and might perform better.)

```bash
mkdir -p checkpoints/dinov2
cd checkpoints
wget https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt
cd dinov2
wget https://dl.fbaipublicfiles.com/dinov2/dinov2_vitl14/dinov2_vitl14_pretrain.pth
cd ../..
```


## 📊 Inference code

⚠️ Disclaimer: This is research code — expect a bit of chaos!

### Reproducing 30-shot SOTA results in Few-shot COCO

Define useful variables and create a folder for results:

```bash
CONFIG=./dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml
CLASS_SPLIT="few_shot_classes"
RESULTS_DIR=work_dirs/few_shot_results
SHOTS=30
SEED=33
GPUS=4

mkdir -p $RESULTS_DIR
FILENAME=few_shot_${SHOTS}shot_seed${SEED}.pkl
```

#### 0. Create reference set

```bash
python dev_hongyi/dataset/few_shot_sampling.py \
        --n-shot $SHOTS \
        --out-path ${RESULTS_DIR}/${FILENAME} \
        --seed $SEED \
        --dataset $CLASS_SPLIT
```

#### 1. Fill memory with references

```bash
python run_lightening.py test --config $CONFIG \
                              --model.test_mode fill_memory \
                              --out_path ${RESULTS_DIR}/memory.ckpt \
                              --model.init_args.model_cfg.memory_bank_cfg.length $SHOTS \
                              --model.init_args.dataset_cfgs.fill_memory.memory_pkl ${RESULTS_DIR}/${FILENAME} \
                              --model.init_args.dataset_cfgs.fill_memory.memory_length $SHOTS \
                              --model.init_args.dataset_cfgs.fill_memory.class_split $CLASS_SPLIT \
                              --trainer.logger.save_dir ${RESULTS_DIR}/ \
                              --trainer.devices $GPUS
```

#### 2. Post-process memory bank

```bash
python run_lightening.py test --config $CONFIG \
                              --model.test_mode postprocess_memory \
                              --model.init_args.model_cfg.memory_bank_cfg.length $SHOTS \
                              --ckpt_path ${RESULTS_DIR}/memory.ckpt \
                              --out_path ${RESULTS_DIR}/memory_postprocessed.ckpt \
                              --trainer.devices 1
```

#### 3. Inference on target images

```bash
python run_lightening.py test --config $CONFIG  \
                              --ckpt_path ${RESULTS_DIR}/memory_postprocessed.ckpt \
                              --model.init_args.test_mode test \
                              --model.init_args.model_cfg.memory_bank_cfg.length $SHOTS \
                              --model.init_args.model_cfg.dataset_name $CLASS_SPLIT \
                              --model.init_args.dataset_cfgs.test.class_split $CLASS_SPLIT \
                              --trainer.logger.save_dir ${RESULTS_DIR}/ \
                              --trainer.devices $GPUS
```

If you'd like to see inference results online (as they are computed), uncomment lines 1746-1749 in `dev_hongyi/models/Sam2MatchingBaseline_noAMG.py` [here](https://github.com/miquel-espinosa/no-time-to-train/blob/main/dev_hongyi/models/Sam2MatchingBaseline_noAMG.py#L1746).
Adjust the score threshold `score_thr` parameter as needed to see more or less segmented instances.
Images will now be saved in `results_analysis/few_shot_classes/`. The image on the left shows the ground truth, the image on the right shows the segmented instances found by our training-free method.

Note that in this example we are using the `few_shot_classes` split, thus, we should only expect to see segmented instances of the classes in this split (not all classes in COCO).

#### Results

After running all images in the validation set, you should obtain:

```
BBOX RESULTS:
  Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.368

SEGM RESULTS:
  Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.342
```
---


## 🔍 Citation

If you use this work, please cite us:

```bibtex
@article{espinosa2025notimetotrain,
  title={No time to train! Training-Free Reference-Based Instance Segmentation},
  author={Miguel Espinosa and Chenhongyi Yang and Linus Ericsson and Steven McDonagh and Elliot J. Crowley},
  journal={arXiv preprint arXiv:2507.02798},
  year={2025},
  primaryclass={cs.CV}
}
```
