README.md
pyproject.toml
setup.py
SAM_2.egg-info/PKG-INFO
SAM_2.egg-info/SOURCES.txt
SAM_2.egg-info/dependency_links.txt
SAM_2.egg-info/requires.txt
SAM_2.egg-info/top_level.txt
dev_hongyi/__init__.py
dev_hongyi/utils.py
dev_hongyi/analysis_scripts/__init__.py
dev_hongyi/analysis_scripts/local_global_corre.py
dev_hongyi/analysis_scripts/local_global_oracleIoU.py
dev_hongyi/analysis_scripts/pcaScore_oracleIoU.py
dev_hongyi/analysis_scripts/pos_neg_by_class.py
dev_hongyi/analysis_scripts/scores_by_class.py
dev_hongyi/analysis_scripts/sim_predIoU_oracleIoU.py
dev_hongyi/dataset/__init__.py
dev_hongyi/dataset/cd_vito_paper_coco_zeroshot_categories.py
dev_hongyi/dataset/change_filename_pascal.py
dev_hongyi/dataset/coco_inst_to_segm.py
dev_hongyi/dataset/coco_ref_dataset.py
dev_hongyi/dataset/coco_to_pkl.py
dev_hongyi/dataset/data_utils.py
dev_hongyi/dataset/download_dataset.py
dev_hongyi/dataset/few_shot_sampling.py
dev_hongyi/dataset/lvis_add_filename.py
dev_hongyi/dataset/lvis_fix_minival_segm.py
dev_hongyi/dataset/metainfo.py
dev_hongyi/dataset/pascal_voc_to_coco.py
dev_hongyi/dataset/sam_bbox_to_segm_batch.py
dev_hongyi/dataset/sample_memory_semantic_ref.py
dev_hongyi/dataset/sample_sub_dataset.py
dev_hongyi/dataset/visualization.py
dev_hongyi/models/SAM2Ref.py
dev_hongyi/models/Sam2Matcher.py
dev_hongyi/models/Sam2MatchingBaseline.py
dev_hongyi/models/Sam2MatchingBaseline_noAMG.py
dev_hongyi/models/__init__.py
dev_hongyi/models/matcher_utils.py
dev_hongyi/models/matching_baseline_utils.py
dev_hongyi/models/matching_foundpose_utils.py
dev_hongyi/models/model_utils.py
sam2/__init__.py
sam2/automatic_mask_generator.py
sam2/build_sam.py
sam2/sam2_image_predictor.py
sam2/sam2_video_predictor.py
sam2/csrc/connected_components.cu
sam2/modeling/__init__.py
sam2/modeling/memory_attention.py
sam2/modeling/memory_encoder.py
sam2/modeling/position_encoding.py
sam2/modeling/sam2_base.py
sam2/modeling/sam2_utils.py
sam2/modeling/backbones/__init__.py
sam2/modeling/backbones/hieradet.py
sam2/modeling/backbones/image_encoder.py
sam2/modeling/backbones/utils.py
sam2/modeling/sam/__init__.py
sam2/modeling/sam/mask_decoder.py
sam2/modeling/sam/prompt_encoder.py
sam2/modeling/sam/transformer.py
sam2/utils/__init__.py
sam2/utils/amg.py
sam2/utils/misc.py
sam2/utils/transforms.py
sam2_configs/__init__.py
sam2_configs/sam2_hiera_b+.yaml
sam2_configs/sam2_hiera_l.yaml
sam2_configs/sam2_hiera_s.yaml
sam2_configs/sam2_hiera_t.yaml