# 🚀 No-Time-to-Train 项目使用指南

## 项目概述

**No-Time-to-Train** 是一个基于参考图像的实例分割方法，无需训练即可实现高精度的目标分割。该项目结合了SAM 2和DinoV2等先进的基础模型，通过语义匹配和记忆库机制实现训练免费的分割。

### 核心特点
- 🚫 **无需训练**: 不需要微调或提示工程
- 🖼️ **基于参考**: 仅使用少量参考图像即可分割新目标
- 🏆 **SOTA性能**: 在COCO、PASCAL VOC等数据集上达到最先进性能
- ⚡ **即插即用**: 支持RTX 5090等最新GPU硬件

---

## 📋 快速开始

### 1. 环境准备

确保你已经完成了RTX 5090的兼容性配置（参考`RTX5090_兼容性解决方案总结.md`）：

```bash
# 激活虚拟环境
conda activate notrain

# 验证GPU状态
python -c "import torch; print('GPU:', torch.cuda.get_device_name(0), 'Capability:', torch.cuda.get_device_capability(0))"
```

### 2. 检查项目结构

```
no-time-to-train/
├── sam2/                    # SAM 2 核心代码
├── dinov2/                  # DinoV2 特征提取器
├── checkpoints/             # 预训练模型
├── notebooks/              # Jupyter 使用示例
├── demo_dataset/           # 演示数据集
├── dev_hongyi/            # 核心算法实现
├── scripts/               # 辅助脚本
└── 配置文件等...
```

---

## 🎯 使用方式

### 方式一：快速演示 (推荐新手)

使用预配置的演示数据快速体验：

```bash
# 运行完整演示流程
bash run_demo.sh
```

演示流程包含：
1. **填充记忆库**: 使用参考图像构建特征记忆
2. **后处理记忆库**: 优化特征表示
3. **推理目标图像**: 在新图像上进行分割

### 方式二：交互式学习

使用Jupyter Notebooks逐步学习：

```bash
# 启动Jupyter
jupyter notebook notebooks/
```

可用的notebooks：
- `image_predictor_example.ipynb` - SAM 2图像分割示例
- `video_predictor_example.ipynb` - SAM 2视频分割示例  
- `automatic_mask_generator_example.ipynb` - 自动掩码生成

### 方式三：完整工作流程

#### 步骤1: 准备数据

创建你的数据集结构：
```
your_dataset/
├── images/
│   ├── reference_1.jpg    # 参考图像
│   ├── reference_2.jpg
│   ├── target_1.jpg       # 目标图像
│   └── target_2.jpg
└── annotations/
    ├── references.json    # 参考图像标注
    └── targets.json       # 目标图像标注（可选）
```

#### 步骤2: 创建参考集

```bash
# 生成few-shot采样
python dev_hongyi/dataset/few_shot_sampling.py \
    --n-shot 30 \
    --out-path results/few_shot_30shot.pkl \
    --seed 33 \
    --dataset your_class_split
```

#### 步骤3: 填充记忆库

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml \
    --model.test_mode fill_memory \
    --out_path results/memory.ckpt \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --model.init_args.dataset_cfgs.fill_memory.memory_pkl results/few_shot_30shot.pkl \
    --trainer.devices 1
```

#### 步骤4: 后处理记忆库

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml \
    --model.test_mode postprocess_memory \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --ckpt_path results/memory.ckpt \
    --out_path results/memory_postprocessed.ckpt \
    --trainer.devices 1
```

#### 步骤5: 推理新图像

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml \
    --ckpt_path results/memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --trainer.devices 1
```

---

## 🔧 配置选项

### 关键参数说明

| 参数 | 说明 | 默认值 | 建议值 |
|------|------|--------|--------|
| `memory_bank_cfg.length` | 记忆库大小 | 10 | 30 (SOTA) |
| `trainer.devices` | GPU数量 | 1 | 1-4 |
| `score_thr` | 分割阈值 | 0.5 | 0.3-0.7 |

### 配置文件

主要配置文件位于 `dev_hongyi/new_exps/`:
- `coco_fewshot_10shot_Sam2L.yaml` - COCO数据集配置
- `coco_fewshot_10shot_Sam2L_cpu.yaml` - CPU版本配置

---

## 📊 性能基准

### COCO Few-Shot 结果

在30-shot设置下：
```
BBOX RESULTS:
Average Precision (AP) @[ IoU=0.50:0.95 ] = 0.368

SEGM RESULTS:  
Average Precision (AP) @[ IoU=0.50:0.95 ] = 0.342
```

### RTX 5090 性能

基于我们的测试：
- **矩阵乘法**: 16-24 TFLOPS
- **推理速度**: ~50ms/图像 (4096×4096)
- **内存使用**: ~8-12GB (批处理大小4)

---

## 🛠️ 高级使用

### 1. 自定义数据集

创建自定义数据配置：

```python
# 在 dev_hongyi/configs/ 中创建新配置
dataset_cfg = {
    'type': 'YourCustomDataset',
    'root': 'path/to/your/data',
    'json_file': 'path/to/annotations.json',
    'class_split': 'your_classes'
}
```

### 2. 调整分割阈值

在推理时实时查看结果：
```python
# 在 dev_hongyi/models/Sam2MatchingBaseline_noAMG.py 中取消注释1746-1749行
# 调整 score_thr 参数查看更多或更少的分割实例
```

### 3. 批量处理

```bash
# 处理整个数据集
for img in images/*.jpg; do
    python run_inference.py --image $img --output results/
done
```

---

## 🔍 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批处理大小或使用梯度检查点
   export CUDA_VISIBLE_DEVICES=0
   # 或在配置中设置 batch_size=1
   ```

2. **检查点文件缺失**
   ```bash
   # 下载必要的检查点
   cd checkpoints
   wget https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt
   ```

3. **特征不匹配错误**
   ```bash
   # 确保输入图像尺寸正确
   # 检查配置文件中的 input_size 设置
   ```

### 性能优化

1. **GPU优化**
   ```python
   # 启用混合精度
   torch.cuda.amp.autocast()
   
   # 优化内存使用
   torch.cuda.empty_cache()
   ```

2. **多GPU加速**
   ```bash
   # 使用多GPU并行
   --trainer.devices 4
   ```

---

## 📝 实用脚本

### 快速测试脚本

```bash
#!/bin/bash
# test_single_image.sh

IMAGE_PATH=$1
OUTPUT_DIR=$2

python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml \
    --ckpt_path checkpoints/memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --model.init_args.test_image $IMAGE_PATH \
    --trainer.logger.save_dir $OUTPUT_DIR \
    --trainer.devices 1
```

### 批量评估脚本

```bash
#!/bin/bash
# batch_eval.sh

DATASET_DIR=$1
RESULTS_DIR=$2

for class in person car dog cat; do
    echo "Processing class: $class"
    python run_lightening.py test \
        --config configs/${class}_config.yaml \
        --ckpt_path checkpoints/memory_${class}.ckpt \
        --trainer.logger.save_dir $RESULTS_DIR/${class}/ \
        --trainer.devices 1
done
```

---

## 🎯 使用建议

### 新手入门流程

1. **先运行演示**: `bash run_demo.sh`
2. **查看notebooks**: 理解核心概念
3. **测试单张图像**: 使用自己的图像
4. **扩展到自定义数据**: 准备自己的数据集

### 最佳实践

1. **参考图像选择**
   - 选择清晰、具有代表性的图像
   - 包含目标对象的多种角度和姿态
   - 确保光照和背景的多样性

2. **性能调优**
   - 根据GPU显存调整批处理大小
   - 使用适当的分割阈值
   - 考虑多尺度测试提高精度

3. **结果分析**
   - 检查分割边界的精确性
   - 分析假阳性和假阴性
   - 根据应用场景调整参数

---

## 📚 相关资源

- **论文**: [No time to train! Training-Free Reference-Based Instance Segmentation](https://arxiv.org/abs/2507.02798)
- **项目页面**: https://miquel-espinosa.github.io/no-time-to-train/
- **SAM 2**: https://github.com/facebookresearch/segment-anything-2
- **DinoV2**: https://github.com/facebookresearch/dinov2

---

## 🤝 社区支持

遇到问题？
- 📝 查看项目GitHub Issues
- 💬 参与开源社区讨论
- 📧 联系论文作者

---

*最后更新: 2025-07-18*  
*兼容性: RTX 5090 ✅ | PyTorch 2.9.0+ ✅* 