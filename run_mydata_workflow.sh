#!/bin/bash
# No-Time-to-Train 用户数据workflow
# 数据: mydata/ (460张参考图像, 4658张测试图像)
# RTX 5090 GPU优化版本

set -e  # 遇到错误时停止

echo "🎯 开始 No-Time-to-Train 用户数据处理"
echo "参考图像: mydata/val_images (460张)"
echo "测试图像: mydata/test_images (4658张)"
echo "结果目录: mydata_results/"
echo ""

# 激活conda环境
echo "🔧 激活conda环境并配置GPU..."
conda activate notrain

# 设置GPU环境 - RTX 5090优化
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

cd /root/Documents/noTTrain

echo "📊 步骤1: 填充记忆库... (使用GPU加速)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --model.test_mode fill_memory \
    --out_path mydata_results/memory.ckpt \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --trainer.devices 1 \
    --trainer.precision 16-mixed

echo ""
echo "🔄 步骤2: 后处理记忆库... (优化特征表示)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --model.test_mode postprocess_memory \
    --ckpt_path mydata_results/memory.ckpt \
    --out_path mydata_results/memory_postprocessed.ckpt \
    --trainer.devices 1

echo ""
echo "🎯 步骤3: 推理测试图像... (4658张图像，GPU加速处理)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --ckpt_path mydata_results/memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --trainer.devices 1 \
    --trainer.precision 16-mixed

echo ""
echo "🎉 用户数据处理完成！"
echo "结果保存在: mydata_results/"
echo ""
echo "📋 处理统计:"
echo "  - 参考图像: 460张 (val_images)"
echo "  - 测试图像: 4658张 (test_images)"  
echo "  - 记忆库大小: 30-shot"
echo "  - GPU加速: RTX 5090"
echo ""
echo "📁 结果文件:"
echo "  - memory.ckpt: 原始记忆库"
echo "  - memory_postprocessed.ckpt: 处理后的记忆库"
echo "  - mydata_experiment/: 详细日志和结果"
echo ""
echo "🔍 查看结果:"
echo "  cd mydata_results/mydata_experiment/"
echo "  tensorboard --logdir=."
