#!/bin/bash
# No-Time-to-Train 演示脚本
# 自动运行完整的推理流程

set -e  # 遇到错误时停止

echo "🎯 开始 No-Time-to-Train 演示"
echo "数据目录: demo_dataset"
echo ""

# 激活conda环境
echo "🔧 激活conda环境..."
conda activate notrain

# 设置CPU环境
export CUDA_VISIBLE_DEVICES=""
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8

cd /root/Documents/noTTrain

echo "📊 步骤1: 填充记忆库..."
python run_lightening.py test \
        --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
        --model.test_mode fill_memory \
        --out_path demo_results/memory.ckpt \
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \
        --model.init_args.dataset_cfgs.fill_memory.root demo_dataset/images \
        --model.init_args.dataset_cfgs.fill_memory.json_file demo_dataset/annotations/references.json \
        --model.init_args.dataset_cfgs.fill_memory.memory_pkl demo_dataset/memory_5shot.pkl \
        --model.init_args.dataset_cfgs.fill_memory.memory_length 5 \
        --trainer.logger.save_dir demo_results/ \
        --trainer.devices 1

echo ""
echo "🔄 步骤2: 后处理记忆库..."
python run_lightening.py test \
        --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
        --model.test_mode postprocess_memory \
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \
        --ckpt_path demo_results/memory.ckpt \
        --out_path demo_results/memory_postprocessed.ckpt \
        --trainer.devices 1

echo ""
echo "🎯 步骤3: 推理目标图像..."
python run_lightening.py test \
        --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
        --ckpt_path demo_results/memory_postprocessed.ckpt \
        --model.init_args.test_mode test \
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \
        --model.init_args.dataset_cfgs.test.root demo_dataset/images \
        --model.init_args.dataset_cfgs.test.json_file demo_dataset/annotations/targets.json \
        --trainer.logger.save_dir demo_results/ \
        --trainer.devices 1

echo ""
echo "🎉 演示完成！"
echo "结果保存在: demo_results/"
