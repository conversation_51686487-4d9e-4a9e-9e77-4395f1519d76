#!/bin/bash
# No-Time-to-Train 30-Shot 工作流程
# 数据: mydata/ (460张参考图像, 4658张测试图像)
# RTX 5090 GPU优化版本 - 30-shot模式

set -e  # 遇到错误时停止

echo "🎯 开始 No-Time-to-Train 30-Shot 工作流程"
echo "=====================================​============="
echo "🔥 模式: 30-shot (SOTA性能模式)"
echo "📊 数据集: mydata"
echo "   - 参考图像: mydata/val_images (460张)"
echo "   - 测试图像: mydata/test_images (4658张)"
echo "   - 类别数: 4 (blister, Black_spot, object, stone)"
echo "📁 结果目录: mydata_results_30shot/"
echo "🖥️  GPU: RTX 5090"
echo ""

# 激活conda环境
echo "🔧 激活conda环境并配置GPU..."
source ~/.bashrc
conda activate notrain

# 设置GPU环境 - RTX 5090优化
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

cd /root/Documents/noTTrain

# 创建结果目录
mkdir -p mydata_results_30shot

echo "📊 步骤1: 填充记忆库... (30-shot模式，使用GPU加速)"
echo "  使用记忆库文件: mydata/memory_30shot.pkl"
echo "  记忆库大小: 30"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --model.test_mode fill_memory \
    --out_path mydata_results_30shot/memory.ckpt \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --model.init_args.dataset_cfgs.fill_memory.memory_pkl ./mydata/memory_30shot.pkl \
    --model.init_args.dataset_cfgs.fill_memory.memory_length 30 \
    --trainer.devices 1 \
    --trainer.precision 16-mixed \
    --trainer.logger.save_dir mydata_results_30shot/

echo ""
echo "🔄 步骤2: 后处理记忆库... (优化特征表示和聚类)"
echo "  对30-shot记忆库进行聚合和K-means聚类优化"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --model.test_mode postprocess_memory \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --ckpt_path mydata_results_30shot/memory.ckpt \
    --out_path mydata_results_30shot/memory_postprocessed.ckpt \
    --trainer.devices 1

echo ""
echo "🎯 步骤3: 推理测试图像... (4658张图像，GPU加速处理)"
echo "  使用30-shot记忆库对测试图像进行分割"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --ckpt_path mydata_results_30shot/memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --model.init_args.model_cfg.memory_bank_cfg.length 30 \
    --trainer.devices 1 \
    --trainer.precision 16-mixed \
    --trainer.logger.save_dir mydata_results_30shot/

echo ""
echo "🎉 30-Shot 工作流程完成！"
echo "=============================="
echo "结果保存在: mydata_results_30shot/"
echo ""
echo "📋 处理统计:"
echo "  - 模式: 30-shot (最佳性能)"
echo "  - 参考图像: 460张 (val_images)"
echo "  - 测试图像: 4658张 (test_images)"  
echo "  - 记忆库大小: 30 shots per category"
echo "  - GPU加速: RTX 5090 + 混合精度"
echo "  - 类别: blister, Black_spot, object, stone"
echo ""
echo "📁 结果文件:"
echo "  - memory.ckpt: 原始30-shot记忆库"
echo "  - memory_postprocessed.ckpt: 优化后的记忆库"
echo "  - lightning_logs/: 详细日志和结果"
echo ""
echo "🎯 预期性能:"
echo "  30-shot模式通常能获得最佳的分割精度和召回率"
echo "  相比10-shot模式，预期AP提升3-5个百分点"
echo ""
echo "📊 查看结果:"
echo "  tensorboard --logdir mydata_results_30shot/lightning_logs" 