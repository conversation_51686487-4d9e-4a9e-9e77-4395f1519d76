# pytorch_lightning==2.1.0
seed_everything: 42
trainer:
  accelerator: cpu
  strategy: auto
  devices: '1'
  num_nodes: 1
  precision: 32
  logger:
  - class_path: pytorch_lightning.loggers.CSVLogger
    init_args:
      save_dir: mydata_results/
      name: lightning_logs
      version: null
      prefix: ''
      flush_logs_every_n_steps: 50
  callbacks:
  - class_path: pytorch_lightning.callbacks.TQDMProgressBar
    init_args:
      refresh_rate: 10
      process_position: 0
  fast_dev_run: false
  max_epochs: null
  min_epochs: null
  max_steps: -1
  min_steps: null
  max_time: null
  limit_train_batches: null
  limit_val_batches: null
  limit_test_batches: null
  limit_predict_batches: null
  overfit_batches: 0.0
  val_check_interval: null
  check_val_every_n_epoch: 1
  num_sanity_val_steps: null
  log_every_n_steps: null
  enable_checkpointing: null
  enable_progress_bar: null
  enable_model_summary: null
  accumulate_grad_batches: 1
  gradient_clip_val: null
  gradient_clip_algorithm: null
  deterministic: null
  benchmark: false
  inference_mode: true
  use_distributed_sampler: true
  profiler: null
  detect_anomaly: false
  barebones: false
  plugins: null
  sync_batchnorm: false
  reload_dataloaders_every_n_epochs: 0
  default_root_dir: null
model:
  class_path: dev_hongyi.pl_wrapper.sam2matcher_pl.Sam2MatcherLightningModel
  init_args:
    model_cfg:
      name: matching_baseline_noAMG
      sam2_cfg_file: sam2_hiera_l.yaml
      sam2_ckpt_path: ./checkpoints/sam2_hiera_large.pt
      sam2_infer_cfgs:
        points_per_side: 16
        testing_point_bs: 64
        iou_thr: 0.4
        nms_thr: 0.5
        num_out_instance: 50
        kmeans_k: 4
        n_pca_components: 3
        cls_num_per_mask: 1
      encoder_cfg:
        name: dinov2_large
        img_size: 518
        patch_size: 14
      encoder_ckpt_path: ./checkpoints/dinov2/dinov2_vitl14_pretrain.pth
      memory_bank_cfg:
        enable: true
        category_num: 20
        length: 10
      memory_bank_cfg.length: '10'
    dataset_cfgs:
      fill_memory:
        name: coco
        root: ./data/coco/train2017
        json_file: ./data/coco/annotations/instances_train2017.json
        memory_pkl: ./data/coco/annotations_refsam2/memory/train2017_allClasses_length10_v1.pkl
        image_size: 518
        memory_length: 10
        context_ratio: 0.2
        norm_img: false
      test:
        name: coco
        root: ./data/coco/val2017
        json_file: ./data/coco/annotations/instances_val2017.json
        image_size: 1024
        norm_img: false
        with_query_points: false
      fill_memory.root: mydata/val_images
      fill_memory.json_file: mydata/val_annotations.json
      fill_memory.memory_pkl: mydata/memory_10shot.pkl
      fill_memory.memory_length: '10'
    data_load_cfgs:
      workers: 2
    test_mode: fill_memory
data: null
out_path: mydata_results/memory.ckpt
out_support_res: null
out_neg_pkl: null
out_neg_json: null
export_result: null
seed: null
n_shot: null
coco_semantic_split: null
optimizer: null
lr_scheduler: null
ckpt_path: null
verbose: true
