# No-Time-to-Train 使用指南

## 🎯 项目原理

**No-Time-to-Train** 是一个**无需训练**的少样本实例分割方法。它的工作原理是：

1. **给定少量参考图像**（比如每个类别10张图像+标注）
2. **构建记忆库**：提取参考图像的特征并存储
3. **自动标注新图像**：通过特征匹配找到相似的对象并分割

## 📊 核心概念

### 什么是"无需训练"？
- 不需要训练神经网络
- 不需要梯度下降优化
- 直接使用预训练的SAM2和DinoV2模型

### 工作流程
```
参考图像 + 标注 → 特征提取 → 记忆库 → 特征匹配 → 新图像分割
```

## 🗂️ 数据准备

### 1. 数据格式要求

项目需要**COCO格式**的数据：

```
your_dataset/
├── images/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── annotations/
    └── instances.json  # COCO格式标注文件
```

### 2. COCO格式标注文件结构

⚠️ **重要**：No-Time-to-Train需要**实例分割标注**，不是简单的目标检测BBOX！

```json
{
  "images": [
    {
      "id": 1,
      "file_name": "image1.jpg",
      "width": 640,
      "height": 480
    }
  ],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],                    # 边界框（可自动计算）
      "segmentation": [[x1,y1,x2,y2,x3,y3,...]],      # 🔥 关键：多边形分割轮廓
      "area": 1234,                                     # 分割区域面积
      "iscrowd": 0
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "person",
      "supercategory": "person"
    }
  ]
}
```

#### 标注类型对比：

**✅ 需要的：实例分割标注**
```json
{
  "bbox": [100, 100, 50, 50],
  "segmentation": [[100,100,150,100,150,150,100,150]],  # 必须有这个！
  "area": 2500
}
```

**❌ 不够的：仅目标检测BBOX**
```json
{
  "bbox": [100, 100, 50, 50]
  // 缺少 "segmentation" 字段 - 这样不行！
}
```
```

### 3. 准备参考数据（Memory Data）

参考数据是用来构建记忆库的少量标注样本：

```bash
# 从完整数据集中采样参考样本
python dev_hongyi/dataset/coco_to_pkl.py \
    --json_path /path/to/your/annotations.json \
    --output_path ./data/your_dataset/memory_10shot.pkl \
    --target_examples 10  # 每个类别10个样本
```

## 🚀 使用步骤

### 步骤0：数据准备

1. **准备图像和标注**
```bash
# 创建数据目录
mkdir -p data/your_dataset/images
mkdir -p data/your_dataset/annotations

# 将图像放入images目录
# 将COCO格式标注文件放入annotations目录
```

2. **生成参考样本**
```bash
python dev_hongyi/dataset/coco_to_pkl.py \
    data/your_dataset/annotations/instances.json \
    data/your_dataset/memory_10shot.pkl \
    10
```

### 步骤1：填充记忆库

使用参考图像构建特征记忆库：

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --model.test_mode fill_memory \
    --out_path ./results/memory.ckpt \
    --model.init_args.model_cfg.memory_bank_cfg.length 10 \
    --model.init_args.dataset_cfgs.fill_memory.root ./data/your_dataset/images \
    --model.init_args.dataset_cfgs.fill_memory.json_file ./data/your_dataset/annotations/instances.json \
    --model.init_args.dataset_cfgs.fill_memory.memory_pkl ./data/your_dataset/memory_10shot.pkl \
    --model.init_args.dataset_cfgs.fill_memory.memory_length 10 \
    --trainer.logger.save_dir ./results/ \
    --trainer.devices 1
```

### 步骤2：后处理记忆库

对记忆库进行聚合和优化：

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --model.test_mode postprocess_memory \
    --model.init_args.model_cfg.memory_bank_cfg.length 10 \
    --ckpt_path ./results/memory.ckpt \
    --out_path ./results/memory_postprocessed.ckpt \
    --trainer.devices 1
```

### 步骤3：推理新图像

在目标图像上进行分割：

```bash
python run_lightening.py test \
    --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --ckpt_path ./results/memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --model.init_args.model_cfg.memory_bank_cfg.length 10 \
    --model.init_args.dataset_cfgs.test.root ./data/your_dataset/images \
    --model.init_args.dataset_cfgs.test.json_file ./data/your_dataset/annotations/instances_test.json \
    --trainer.logger.save_dir ./results/ \
    --trainer.devices 1
```

## 📝 实际使用示例

### 场景：标注新的动物图像

假设您有：
- **参考数据**：10张猫的图像 + 分割标注
- **目标**：自动标注100张新的猫图像

#### 1. 准备数据
```
animal_dataset/
├── images/
│   ├── cat_ref_001.jpg    # 参考图像
│   ├── cat_ref_002.jpg
│   ├── ...
│   ├── cat_new_001.jpg    # 待标注图像
│   ├── cat_new_002.jpg
│   └── ...
└── annotations/
    ├── cat_references.json  # 参考图像的标注
    └── cat_targets.json     # 待标注图像（只有图像信息，无标注）
```

#### 2. 生成记忆库
```bash
# 从参考标注中生成记忆库数据
python dev_hongyi/dataset/coco_to_pkl.py \
    animal_dataset/annotations/cat_references.json \
    animal_dataset/cat_memory_10shot.pkl \
    10
```

#### 3. 运行三步流程
```bash
# 步骤1：填充记忆库
python run_lightening.py test --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --model.test_mode fill_memory \
    --model.init_args.dataset_cfgs.fill_memory.root animal_dataset/images \
    --model.init_args.dataset_cfgs.fill_memory.json_file animal_dataset/annotations/cat_references.json \
    --model.init_args.dataset_cfgs.fill_memory.memory_pkl animal_dataset/cat_memory_10shot.pkl \
    --out_path ./results/cat_memory.ckpt

# 步骤2：后处理
python run_lightening.py test --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --model.test_mode postprocess_memory \
    --ckpt_path ./results/cat_memory.ckpt \
    --out_path ./results/cat_memory_postprocessed.ckpt

# 步骤3：推理
python run_lightening.py test --config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml \
    --ckpt_path ./results/cat_memory_postprocessed.ckpt \
    --model.init_args.test_mode test \
    --model.init_args.dataset_cfgs.test.root animal_dataset/images \
    --model.init_args.dataset_cfgs.test.json_file animal_dataset/annotations/cat_targets.json
```

#### 4. 结果
- 系统会自动在新图像中找到并分割出猫
- 结果保存为COCO格式，可以直接用于训练其他模型

## 🔧 高级配置

### 调整参数
- `memory_length`: 每个类别的参考样本数量
- `points_per_side`: SAM2采样点密度
- `iou_thr`: IoU阈值
- `num_out_instance`: 最大输出实例数

### CPU优化
使用我们提供的CPU配置文件：
```bash
--config dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml
```

## ❓ 常见问题

### Q: 需要多少参考样本？
A: 通常每个类别5-30个样本。更多样本通常带来更好的效果。

### Q: 支持哪些图像格式？
A: 支持常见格式：JPG, PNG, BMP等。

### Q: 如何提高分割质量？
A: 
1. 增加参考样本数量
2. 确保参考样本的多样性
3. 调整IoU阈值和其他参数

### Q: 可以处理多个类别吗？
A: 可以！只需在标注文件中包含多个类别的数据。

### Q: 我只有BBOX标注，没有分割标注怎么办？
A: 可以使用我们提供的转换工具：
```bash
python scripts/bbox_to_segmentation.py
```
这个工具会使用SAM2自动将BBOX转换为精确的分割标注。

### Q: 推荐的标注工具有哪些？
A:
- **Labelme**：简单易用，直接输出多边形
- **CVAT**：专业标注平台
- **Roboflow**：在线标注工具
- **Supervisely**：企业级标注平台
