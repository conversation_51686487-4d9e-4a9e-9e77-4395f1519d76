#!/bin/bash

# MyData 10-shot 完整工作流程
# 使用3张测试图像进行快速验证

set -e  # 遇到错误立即退出

echo "🚀 开始 MyData 10-shot 工作流程"
echo "=" * 50

# 激活conda环境
source ~/.bashrc
conda activate notrain

# 创建结果目录
mkdir -p mydata_results_10shot

echo "🎯 步骤1: 填充记忆库... (10-shot模式)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --model.test_mode fill_memory \
    --out_path mydata_results_10shot/memory.ckpt \
    --trainer.devices 1 \
    --trainer.precision 16-mixed \
    --trainer.logger.save_dir mydata_results_10shot/

echo "🔧 步骤2: 后处理记忆库... (优化特征表示和聚类)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --ckpt_path mydata_results_10shot/memory.ckpt \
    --model.test_mode postprocess_memory \
    --out_path mydata_results_10shot/memory_postprocessed.ckpt \
    --trainer.devices 1 \
    --trainer.precision 16-mixed \
    --trainer.logger.save_dir mydata_results_10shot/

echo "🎯 步骤3: 推理测试图像... (3张图像，GPU加速处理)"
python run_lightening.py test \
    --config dev_hongyi/new_exps/mydata_config.yaml \
    --ckpt_path mydata_results_10shot/memory_postprocessed.ckpt \
    --model.test_mode test \
    --trainer.devices 1 \
    --trainer.precision 16-mixed \
    --trainer.logger.save_dir mydata_results_10shot/

echo "🎉 工作流程完成！"
echo "📊 结果保存在: mydata_results_10shot/"
echo "📸 可视化结果保存在: mydata_results_10shot/visualizations/"
echo "📋 详细日志: mydata_results_10shot/lightning_logs/"
