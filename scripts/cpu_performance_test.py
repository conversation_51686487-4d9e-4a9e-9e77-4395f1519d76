#!/usr/bin/env python3
"""
CPU性能优化和基准测试脚本
用于测试和优化no-time-to-train项目在CPU模式下的性能
"""

import os
import time
import torch
import numpy as np
from PIL import Image
import warnings
import psutil
from contextlib import contextmanager

# 设置CPU环境
os.environ['CUDA_VISIBLE_DEVICES'] = ''
warnings.filterwarnings('ignore')

@contextmanager
def timer(description):
    """计时器上下文管理器"""
    start = time.time()
    yield
    end = time.time()
    print(f"   {description}: {end - start:.2f}秒")

def setup_cpu_optimization():
    """设置CPU优化参数"""
    print("🚀 设置CPU性能优化...")
    
    # 获取CPU信息
    cpu_count = os.cpu_count()
    optimal_threads = min(cpu_count, 8)
    
    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
    os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
    
    # 设置PyTorch参数
    torch.set_num_threads(optimal_threads)
    torch.set_num_interop_threads(optimal_threads)
    
    # 启用JIT优化
    torch.jit.set_fusion_strategy([('STATIC', 2), ('DYNAMIC', 2)])
    
    # 设置推理模式
    torch.set_grad_enabled(False)
    
    print(f"✅ CPU优化设置完成 (使用{optimal_threads}个线程)")
    return optimal_threads

def create_test_images(sizes=[(518, 518), (1024, 1024)]):
    """创建不同尺寸的测试图像"""
    print("📸 创建测试图像...")
    images = {}
    for size in sizes:
        img_array = np.random.randint(0, 255, (*size, 3), dtype=np.uint8)
        images[f"{size[0]}x{size[1]}"] = Image.fromarray(img_array)
    print(f"✅ 创建了{len(images)}个测试图像")
    return images

def benchmark_dinov2(images):
    """DinoV2性能基准测试"""
    print("🦕 DinoV2性能测试...")
    
    try:
        import dinov2.models.vision_transformer as vits
        from torchvision import transforms
        
        # 创建模型
        with timer("模型加载"):
            model = vits.__dict__['vit_large'](
                patch_size=14, img_size=518, init_values=1.0, block_chunks=0
            )
            checkpoint = torch.load('checkpoints/dinov2/dinov2_vitl14_pretrain.pth', 
                                  map_location='cpu', weights_only=True)
            model.load_state_dict(checkpoint, strict=True)
            model.eval().cpu()
        
        # 准备数据变换
        transform = transforms.Compose([
            transforms.Resize((518, 518)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        results = {}
        for size_name, image in images.items():
            print(f"   测试图像尺寸: {size_name}")
            
            # 数据预处理
            with timer("数据预处理"):
                input_tensor = transform(image).unsqueeze(0).cpu()
            
            # 推理测试
            with timer("特征提取"):
                with torch.no_grad():
                    features = model(input_tensor)
            
            results[size_name] = {
                'input_shape': input_tensor.shape,
                'output_shape': features.shape,
                'feature_dim': features.size(-1)
            }
            
            print(f"     输入: {input_tensor.shape}, 输出: {features.shape}")
        
        del model, checkpoint
        return results
        
    except Exception as e:
        print(f"❌ DinoV2测试失败: {e}")
        return {}

def benchmark_sam2(images):
    """SAM2性能基准测试"""
    print("🎯 SAM2性能测试...")
    
    try:
        from sam2.build_sam import build_sam2
        from sam2.sam2_image_predictor import SAM2ImagePredictor
        
        # 构建模型
        with timer("模型加载"):
            model = build_sam2('sam2_hiera_l', 'checkpoints/sam2_hiera_large.pt', device='cpu')
            predictor = SAM2ImagePredictor(model)
        
        results = {}
        for size_name, image in images.items():
            print(f"   测试图像尺寸: {size_name}")
            
            # 图像设置
            with timer("图像设置"):
                image_array = np.array(image)
                predictor.set_image(image_array)
            
            # 点提示分割
            with timer("点提示分割"):
                h, w = image_array.shape[:2]
                point = np.array([[w//2, h//2]])
                label = np.array([1])
                masks, scores, _ = predictor.predict(
                    point_coords=point, point_labels=label, multimask_output=True
                )
            
            # 框提示分割
            with timer("框提示分割"):
                box = np.array([w//4, h//4, 3*w//4, 3*h//4])
                masks2, scores2, _ = predictor.predict(
                    box=box[None, :], multimask_output=False
                )
            
            results[size_name] = {
                'image_shape': image_array.shape,
                'point_masks': len(masks),
                'point_scores': scores.max(),
                'box_masks': len(masks2),
                'box_scores': scores2.max()
            }
            
            print(f"     图像: {image_array.shape}, 点分割: {len(masks)}个mask, 框分割: {len(masks2)}个mask")
        
        del model, predictor
        return results
        
    except Exception as e:
        print(f"❌ SAM2测试失败: {e}")
        return {}

def memory_benchmark():
    """内存使用基准测试"""
    print("💾 内存使用测试...")
    
    process = psutil.Process()
    
    # 基础内存
    base_memory = process.memory_info().rss / 1024**2
    print(f"   基础内存: {base_memory:.1f} MB")
    
    # 测试内存峰值
    peak_memory = base_memory
    
    try:
        # 加载DinoV2
        import dinov2.models.vision_transformer as vits
        model1 = vits.__dict__['vit_large'](patch_size=14, img_size=518)
        checkpoint = torch.load('checkpoints/dinov2/dinov2_vitl14_pretrain.pth', 
                              map_location='cpu', weights_only=True)
        model1.load_state_dict(checkpoint, strict=True)
        
        dinov2_memory = process.memory_info().rss / 1024**2
        peak_memory = max(peak_memory, dinov2_memory)
        print(f"   DinoV2加载后: {dinov2_memory:.1f} MB")
        
        # 加载SAM2
        from sam2.build_sam import build_sam2
        model2 = build_sam2('sam2_hiera_l', 'checkpoints/sam2_hiera_large.pt', device='cpu')
        
        sam2_memory = process.memory_info().rss / 1024**2
        peak_memory = max(peak_memory, sam2_memory)
        print(f"   SAM2加载后: {sam2_memory:.1f} MB")
        
        # 清理
        del model1, model2, checkpoint
        
        final_memory = process.memory_info().rss / 1024**2
        print(f"   清理后: {final_memory:.1f} MB")
        print(f"   峰值内存: {peak_memory:.1f} MB")
        
        return {
            'base': base_memory,
            'dinov2': dinov2_memory,
            'sam2': sam2_memory,
            'peak': peak_memory,
            'final': final_memory
        }
        
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return {}

def main():
    """主函数"""
    print("🎯 CPU性能优化和基准测试")
    print("=" * 50)
    
    # 设置优化
    optimal_threads = setup_cpu_optimization()
    print()
    
    # 创建测试图像
    test_images = create_test_images()
    print()
    
    # DinoV2基准测试
    dinov2_results = benchmark_dinov2(test_images)
    print()
    
    # SAM2基准测试
    sam2_results = benchmark_sam2(test_images)
    print()
    
    # 内存基准测试
    memory_results = memory_benchmark()
    print()
    
    # 总结报告
    print("📊 性能测试总结:")
    print(f"   CPU线程数: {optimal_threads}")
    print(f"   测试图像数: {len(test_images)}")
    print(f"   DinoV2测试: {'✅ 成功' if dinov2_results else '❌ 失败'}")
    print(f"   SAM2测试: {'✅ 成功' if sam2_results else '❌ 失败'}")
    print(f"   内存测试: {'✅ 成功' if memory_results else '❌ 失败'}")
    
    if memory_results:
        print(f"   峰值内存使用: {memory_results.get('peak', 0):.1f} MB")
    
    print()
    print("🎉 CPU性能测试完成!")

if __name__ == "__main__":
    main()
