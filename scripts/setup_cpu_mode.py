#!/usr/bin/env python3
"""
CPU模式设置脚本
用于配置no-time-to-train项目在CPU模式下运行
"""

import os
import torch
import warnings

def setup_cpu_environment():
    """设置CPU优化环境变量"""
    print("🔧 配置CPU优化环境...")
    
    # 设置PyTorch使用CPU
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    # 设置线程数（根据CPU核心数调整）
    cpu_count = os.cpu_count()
    optimal_threads = min(cpu_count, 8)  # 限制最大线程数
    
    os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
    os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
    
    # 设置PyTorch线程数
    torch.set_num_threads(optimal_threads)
    
    # 禁用CUDA相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='torch.cuda')
    
    print(f"✅ CPU环境配置完成:")
    print(f"   CPU核心数: {cpu_count}")
    print(f"   使用线程数: {optimal_threads}")
    print(f"   PyTorch线程数: {torch.get_num_threads()}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    print(f"   强制CPU模式: {'是' if not torch.cuda.is_available() or os.environ.get('CUDA_VISIBLE_DEVICES') == '' else '否'}")

def force_cpu_device():
    """强制所有模型使用CPU设备"""
    print("🔧 强制CPU设备模式...")
    
    # 猴子补丁：重写cuda()方法
    original_cuda = torch.Tensor.cuda
    original_to = torch.Tensor.to
    
    def force_cpu_cuda(self, *args, **kwargs):
        """重写cuda方法，强制返回CPU tensor"""
        return self.cpu()
    
    def force_cpu_to(self, device=None, *args, **kwargs):
        """重写to方法，强制使用CPU"""
        if device is not None and 'cuda' in str(device):
            device = 'cpu'
        return original_to(self, device, *args, **kwargs)
    
    # 应用补丁
    torch.Tensor.cuda = force_cpu_cuda
    torch.Tensor.to = force_cpu_to
    
    print("✅ 强制CPU设备模式已启用")

def optimize_cpu_inference():
    """优化CPU推理性能"""
    print("🚀 优化CPU推理性能...")
    
    # 启用JIT编译
    torch.jit.set_fusion_strategy([('STATIC', 2), ('DYNAMIC', 2)])
    
    # 设置推理模式
    torch.set_grad_enabled(False)
    
    # 优化内存使用
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True
    
    print("✅ CPU推理优化完成")

def check_cpu_setup():
    """检查CPU设置是否正确"""
    print("🔍 检查CPU设置...")
    
    # 检查设备
    device = torch.device('cpu')
    test_tensor = torch.randn(10, 10, device=device)
    
    print(f"✅ 测试tensor设备: {test_tensor.device}")
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ CPU线程数: {torch.get_num_threads()}")
    
    # 测试基本操作
    result = torch.mm(test_tensor, test_tensor.T)
    print(f"✅ CPU矩阵运算测试通过: {result.shape}")
    
    return True

if __name__ == "__main__":
    print("🎯 启动CPU模式设置...")
    print("=" * 50)
    
    setup_cpu_environment()
    print()
    
    force_cpu_device()
    print()
    
    optimize_cpu_inference()
    print()
    
    check_cpu_setup()
    print()
    
    print("🎉 CPU模式设置完成！")
    print("现在可以运行no-time-to-train项目了。")
