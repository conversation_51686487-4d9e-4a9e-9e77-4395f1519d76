#!/usr/bin/env python3
"""
CPU模式完整推理演示脚本
演示no-time-to-train项目在CPU模式下的完整工作流程
"""

import os
import time
import torch
import numpy as np
from PIL import Image, ImageDraw
import warnings
import json
from pathlib import Path

# 设置CPU环境
os.environ['CUDA_VISIBLE_DEVICES'] = ''
torch.set_num_threads(8)
warnings.filterwarnings('ignore')

def setup_cpu_environment():
    """设置CPU优化环境"""
    print("🔧 设置CPU优化环境...")
    
    # 设置线程数
    optimal_threads = min(os.cpu_count(), 8)
    os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
    torch.set_num_threads(optimal_threads)
    torch.set_grad_enabled(False)
    
    print(f"✅ CPU环境设置完成 (使用{optimal_threads}个线程)")
    return optimal_threads

def create_demo_images():
    """创建演示图像"""
    print("📸 创建演示图像...")
    
    # 创建输出目录
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建几个不同的测试图像
    images = {}
    
    # 图像1: 简单几何形状
    img1 = Image.new('RGB', (518, 518), 'white')
    draw1 = ImageDraw.Draw(img1)
    draw1.rectangle([100, 100, 200, 200], fill='red')
    draw1.ellipse([300, 300, 400, 400], fill='blue')
    draw1.polygon([(250, 50), (200, 150), (300, 150)], fill='green')
    img1.save(output_dir / "demo_shapes.jpg")
    images['shapes'] = img1
    
    # 图像2: 随机纹理
    img2_array = np.random.randint(0, 255, (518, 518, 3), dtype=np.uint8)
    # 添加一些结构
    img2_array[200:300, 200:300] = [255, 0, 0]  # 红色方块
    img2_array[350:450, 350:450] = [0, 255, 0]  # 绿色方块
    img2 = Image.fromarray(img2_array)
    img2.save(output_dir / "demo_texture.jpg")
    images['texture'] = img2
    
    # 图像3: 渐变背景
    img3 = Image.new('RGB', (518, 518), 'white')
    draw3 = ImageDraw.Draw(img3)
    for i in range(518):
        color = int(255 * i / 518)
        draw3.line([(i, 0), (i, 518)], fill=(color, color, 255))
    # 添加一些对象
    draw3.ellipse([150, 150, 250, 250], fill='yellow')
    draw3.rectangle([300, 100, 400, 200], fill='purple')
    img3.save(output_dir / "demo_gradient.jpg")
    images['gradient'] = img3
    
    print(f"✅ 创建了{len(images)}个演示图像，保存在 {output_dir}")
    return images, output_dir

def test_dinov2_features(images):
    """测试DinoV2特征提取"""
    print("🦕 DinoV2特征提取测试...")
    
    try:
        import dinov2.models.vision_transformer as vits
        from torchvision import transforms
        
        # 加载模型
        start_time = time.time()
        model = vits.__dict__['vit_large'](
            patch_size=14, img_size=518, init_values=1.0, block_chunks=0
        )
        checkpoint = torch.load('checkpoints/dinov2/dinov2_vitl14_pretrain.pth', 
                              map_location='cpu', weights_only=True)
        model.load_state_dict(checkpoint, strict=True)
        model.eval().cpu()
        load_time = time.time() - start_time
        
        # 数据变换
        transform = transforms.Compose([
            transforms.Resize((518, 518)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        features_dict = {}
        for name, image in images.items():
            print(f"   处理图像: {name}")
            
            # 预处理
            input_tensor = transform(image).unsqueeze(0).cpu()
            
            # 特征提取
            start_time = time.time()
            with torch.no_grad():
                features = model(input_tensor)
            inference_time = time.time() - start_time
            
            features_dict[name] = {
                'features': features.cpu().numpy(),
                'inference_time': inference_time,
                'shape': features.shape
            }
            
            print(f"     特征形状: {features.shape}, 推理时间: {inference_time:.3f}秒")
        
        print(f"✅ DinoV2测试完成 (模型加载: {load_time:.2f}秒)")
        del model, checkpoint
        return features_dict
        
    except Exception as e:
        print(f"❌ DinoV2测试失败: {e}")
        return {}

def test_sam2_segmentation(images, output_dir):
    """测试SAM2分割"""
    print("🎯 SAM2分割测试...")
    
    try:
        from sam2.build_sam import build_sam2
        from sam2.sam2_image_predictor import SAM2ImagePredictor
        
        # 加载模型
        start_time = time.time()
        model = build_sam2('sam2_hiera_l', 'checkpoints/sam2_hiera_large.pt', device='cpu')
        predictor = SAM2ImagePredictor(model)
        load_time = time.time() - start_time
        
        segmentation_results = {}
        for name, image in images.items():
            print(f"   分割图像: {name}")
            
            # 设置图像
            image_array = np.array(image)
            predictor.set_image(image_array)
            
            h, w = image_array.shape[:2]
            
            # 测试不同的提示方式
            results = {}
            
            # 1. 中心点提示
            start_time = time.time()
            center_point = np.array([[w//2, h//2]])
            center_label = np.array([1])
            masks1, scores1, _ = predictor.predict(
                point_coords=center_point, point_labels=center_label, multimask_output=True
            )
            center_time = time.time() - start_time
            results['center_point'] = {
                'masks': len(masks1), 'best_score': scores1.max(), 'time': center_time
            }
            
            # 2. 多点提示
            start_time = time.time()
            multi_points = np.array([[w//4, h//4], [3*w//4, 3*h//4]])
            multi_labels = np.array([1, 1])
            masks2, scores2, _ = predictor.predict(
                point_coords=multi_points, point_labels=multi_labels, multimask_output=False
            )
            multi_time = time.time() - start_time
            results['multi_point'] = {
                'masks': len(masks2), 'best_score': scores2.max(), 'time': multi_time
            }
            
            # 3. 框提示
            start_time = time.time()
            box = np.array([w//4, h//4, 3*w//4, 3*h//4])
            masks3, scores3, _ = predictor.predict(
                box=box[None, :], multimask_output=False
            )
            box_time = time.time() - start_time
            results['box'] = {
                'masks': len(masks3), 'best_score': scores3.max(), 'time': box_time
            }
            
            # 保存最佳mask
            best_mask = masks1[np.argmax(scores1)]
            mask_image = Image.fromarray((best_mask * 255).astype(np.uint8))
            mask_image.save(output_dir / f"mask_{name}.png")
            
            # 创建叠加图像
            overlay = image.copy()
            overlay_array = np.array(overlay)
            overlay_array[best_mask > 0.5] = [255, 0, 0]  # 红色叠加
            overlay_image = Image.fromarray(overlay_array)
            overlay_image.save(output_dir / f"overlay_{name}.jpg")
            
            segmentation_results[name] = results
            
            print(f"     中心点: {results['center_point']['masks']}个mask, "
                  f"分数: {results['center_point']['best_score']:.3f}, "
                  f"时间: {results['center_point']['time']:.3f}秒")
        
        print(f"✅ SAM2测试完成 (模型加载: {load_time:.2f}秒)")
        del model, predictor
        return segmentation_results
        
    except Exception as e:
        print(f"❌ SAM2测试失败: {e}")
        return {}

def save_results(dinov2_results, sam2_results, output_dir):
    """保存测试结果"""
    print("💾 保存测试结果...")
    
    # 准备结果数据
    results = {
        'dinov2': {},
        'sam2': {},
        'summary': {
            'total_images': len(dinov2_results),
            'dinov2_success': len(dinov2_results) > 0,
            'sam2_success': len(sam2_results) > 0
        }
    }
    
    # DinoV2结果
    for name, data in dinov2_results.items():
        results['dinov2'][name] = {
            'feature_shape': list(data['shape']),
            'inference_time': data['inference_time'],
            'feature_norm': float(np.linalg.norm(data['features']))
        }
    
    # SAM2结果
    for name, data in sam2_results.items():
        results['sam2'][name] = data
    
    # 保存JSON结果
    with open(output_dir / "results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"✅ 结果已保存到 {output_dir / 'results.json'}")
    return results

def main():
    """主函数"""
    print("🎯 No-Time-to-Train CPU模式完整推理演示")
    print("=" * 60)
    
    # 设置环境
    setup_cpu_environment()
    print()
    
    # 创建演示图像
    images, output_dir = create_demo_images()
    print()
    
    # DinoV2特征提取
    dinov2_results = test_dinov2_features(images)
    print()
    
    # SAM2分割
    sam2_results = test_sam2_segmentation(images, output_dir)
    print()
    
    # 保存结果
    final_results = save_results(dinov2_results, sam2_results, output_dir)
    print()
    
    # 总结
    print("📊 演示总结:")
    print(f"   处理图像数: {len(images)}")
    print(f"   DinoV2状态: {'✅ 成功' if dinov2_results else '❌ 失败'}")
    print(f"   SAM2状态: {'✅ 成功' if sam2_results else '❌ 失败'}")
    print(f"   输出目录: {output_dir}")
    print(f"   生成文件: 图像 + mask + 叠加图 + 结果JSON")
    
    if dinov2_results and sam2_results:
        avg_dinov2_time = np.mean([r['inference_time'] for r in dinov2_results.values()])
        avg_sam2_time = np.mean([
            r['center_point']['time'] for r in sam2_results.values()
        ])
        print(f"   平均DinoV2推理时间: {avg_dinov2_time:.3f}秒")
        print(f"   平均SAM2推理时间: {avg_sam2_time:.3f}秒")
    
    print()
    print("🎉 CPU模式完整推理演示完成!")
    print(f"请查看 {output_dir} 目录中的结果文件")

if __name__ == "__main__":
    main()
