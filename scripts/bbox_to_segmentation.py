#!/usr/bin/env python3
"""
将BBOX标注转换为实例分割标注
使用SAM2从边界框生成精确的分割mask
"""

import os
import json
import numpy as np
from PIL import Image
import torch
import warnings
from pathlib import Path
import pycocotools.mask as mask_utils

# 设置CPU环境
os.environ['CUDA_VISIBLE_DEVICES'] = ''
torch.set_num_threads(8)
warnings.filterwarnings('ignore')

def bbox_to_segmentation_with_sam2(image_path, bbox, sam2_predictor):
    """
    使用SAM2将边界框转换为分割mask
    
    Args:
        image_path: 图像路径
        bbox: [x, y, width, height] 格式的边界框
        sam2_predictor: SAM2预测器
    
    Returns:
        segmentation: COCO格式的分割多边形
        area: 分割区域面积
    """
    # 加载图像
    image = np.array(Image.open(image_path))
    sam2_predictor.set_image(image)
    
    # 转换bbox格式：[x, y, width, height] -> [x1, y1, x2, y2]
    x, y, w, h = bbox
    box = np.array([x, y, x + w, y + h])
    
    # 使用SAM2进行分割
    masks, scores, _ = sam2_predictor.predict(
        box=box[None, :],  # 添加batch维度
        multimask_output=False
    )
    
    # 获取最佳mask
    best_mask = masks[0]  # shape: (H, W)
    
    # 转换为COCO格式的多边形
    # 首先转换为RLE格式
    rle = mask_utils.encode(np.asfortranarray(best_mask.astype(np.uint8)))
    
    # 计算面积
    area = float(mask_utils.area(rle))
    
    # 转换为多边形格式
    contours = mask_utils.frPyObjects([rle], best_mask.shape[0], best_mask.shape[1])
    polygons = []
    
    # 使用OpenCV找到轮廓
    import cv2
    contours_cv, _ = cv2.findContours(
        best_mask.astype(np.uint8), 
        cv2.RETR_EXTERNAL, 
        cv2.CHAIN_APPROX_SIMPLE
    )
    
    for contour in contours_cv:
        # 简化轮廓
        epsilon = 0.005 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        if len(approx) >= 3:  # 至少3个点才能形成多边形
            polygon = approx.flatten().tolist()
            # 确保坐标是浮点数
            polygon = [float(coord) for coord in polygon]
            polygons.append(polygon)
    
    return polygons, area

def convert_bbox_to_segmentation(input_json, output_json, images_dir):
    """
    将包含BBOX的COCO标注转换为包含分割的标注
    
    Args:
        input_json: 输入的BBOX标注文件
        output_json: 输出的分割标注文件
        images_dir: 图像目录
    """
    print("🔄 开始转换BBOX到分割标注...")
    
    # 加载输入标注
    with open(input_json, 'r') as f:
        coco_data = json.load(f)
    
    # 初始化SAM2
    print("🎯 初始化SAM2...")
    from sam2.build_sam import build_sam2
    from sam2.sam2_image_predictor import SAM2ImagePredictor
    
    sam2_model = build_sam2('sam2_hiera_l', 'checkpoints/sam2_hiera_large.pt', device='cpu')
    predictor = SAM2ImagePredictor(sam2_model)
    
    # 创建图像ID到文件名的映射
    id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
    
    # 处理每个标注
    updated_annotations = []
    
    for i, ann in enumerate(coco_data['annotations']):
        print(f"处理标注 {i+1}/{len(coco_data['annotations'])}: 图像ID {ann['image_id']}")
        
        # 获取图像路径
        filename = id_to_filename[ann['image_id']]
        image_path = Path(images_dir) / filename
        
        if not image_path.exists():
            print(f"⚠️  图像不存在: {image_path}")
            continue
        
        # 获取边界框
        bbox = ann['bbox']  # [x, y, width, height]
        
        try:
            # 使用SAM2生成分割
            segmentation, area = bbox_to_segmentation_with_sam2(
                str(image_path), bbox, predictor
            )
            
            # 更新标注
            updated_ann = ann.copy()
            updated_ann['segmentation'] = segmentation
            updated_ann['area'] = area
            
            updated_annotations.append(updated_ann)
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            # 保留原始标注（没有分割）
            updated_annotations.append(ann)
    
    # 更新COCO数据
    coco_data['annotations'] = updated_annotations
    
    # 保存结果
    with open(output_json, 'w') as f:
        json.dump(coco_data, f, indent=2)
    
    print(f"✅ 转换完成！结果保存到: {output_json}")
    print(f"   处理了 {len(updated_annotations)} 个标注")
    
    # 清理
    del sam2_model, predictor

def create_example_bbox_data():
    """创建示例BBOX数据用于演示"""
    print("📁 创建示例BBOX数据...")
    
    # 创建目录
    demo_dir = Path("demo_bbox_dataset")
    images_dir = demo_dir / "images"
    annotations_dir = demo_dir / "annotations"
    
    images_dir.mkdir(parents=True, exist_ok=True)
    annotations_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建示例图像
    from PIL import ImageDraw
    
    images = []
    annotations = []
    
    for i in range(3):
        # 创建图像：白色背景 + 红色矩形
        img = Image.new('RGB', (400, 400), 'white')
        draw = ImageDraw.Draw(img)
        
        # 矩形位置
        x = 50 + i * 30
        y = 50 + i * 20
        w = 100 + i * 20
        h = 80 + i * 15
        
        draw.rectangle([x, y, x + w, y + h], fill='red')
        
        # 保存图像
        img_filename = f"bbox_example_{i:03d}.jpg"
        img.save(images_dir / img_filename)
        
        # 图像信息
        images.append({
            "id": i + 1,
            "file_name": img_filename,
            "width": 400,
            "height": 400
        })
        
        # BBOX标注（注意：没有segmentation字段）
        annotations.append({
            "id": i + 1,
            "image_id": i + 1,
            "category_id": 1,
            "bbox": [float(x), float(y), float(w), float(h)],
            "area": float(w * h),
            "iscrowd": 0
            # 注意：这里没有 "segmentation" 字段
        })
    
    # 创建COCO格式文件
    coco_data = {
        "images": images,
        "annotations": annotations,
        "categories": [{
            "id": 1,
            "name": "red_rectangle",
            "supercategory": "shape"
        }]
    }
    
    bbox_json = annotations_dir / "bbox_only.json"
    with open(bbox_json, 'w') as f:
        json.dump(coco_data, f, indent=2)
    
    print(f"✅ 示例BBOX数据创建完成:")
    print(f"   图像: {len(images)}张")
    print(f"   BBOX标注文件: {bbox_json}")
    
    return demo_dir, bbox_json

def main():
    """主函数"""
    print("🎯 BBOX到分割标注转换工具")
    print("=" * 50)
    
    # 创建示例数据
    demo_dir, bbox_json = create_example_bbox_data()
    print()
    
    # 转换为分割标注
    output_json = demo_dir / "annotations" / "with_segmentation.json"
    images_dir = demo_dir / "images"
    
    convert_bbox_to_segmentation(
        input_json=bbox_json,
        output_json=output_json,
        images_dir=images_dir
    )
    
    print()
    print("📋 使用说明:")
    print("1. 查看原始BBOX标注:")
    print(f"   cat {bbox_json}")
    print()
    print("2. 查看转换后的分割标注:")
    print(f"   cat {output_json}")
    print()
    print("3. 对比两个文件，注意分割标注中新增的 'segmentation' 字段")
    print()
    print("🔧 转换您自己的数据:")
    print("python scripts/bbox_to_segmentation.py")
    print("# 然后修改 convert_bbox_to_segmentation() 调用中的路径")

if __name__ == "__main__":
    main()
