#!/usr/bin/env python3
"""
No-Time-to-Train 简单使用示例
演示如何用少量参考图像自动标注新图像
"""

import os
import json
import numpy as np
from PIL import Image, ImageDraw
import torch
import warnings
from pathlib import Path

# 设置CPU环境
os.environ['CUDA_VISIBLE_DEVICES'] = ''
torch.set_num_threads(8)
warnings.filterwarnings('ignore')

def create_demo_dataset():
    """创建演示数据集"""
    print("📁 创建演示数据集...")
    
    # 创建目录结构
    demo_dir = Path("demo_dataset")
    images_dir = demo_dir / "images"
    annotations_dir = demo_dir / "annotations"
    
    images_dir.mkdir(parents=True, exist_ok=True)
    annotations_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建参考图像（带标注）
    reference_images = []
    reference_annotations = []
    
    for i in range(5):  # 5张参考图像
        # 创建图像：白色背景 + 红色圆形
        img = Image.new('RGB', (400, 400), 'white')
        draw = ImageDraw.Draw(img)
        
        # 随机位置的圆形
        center_x = 100 + i * 50
        center_y = 100 + i * 40
        radius = 30 + i * 5
        
        draw.ellipse([
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ], fill='red')
        
        # 保存图像
        img_filename = f"ref_circle_{i:03d}.jpg"
        img.save(images_dir / img_filename)
        
        # 创建标注数据
        reference_images.append({
            "id": i + 1,
            "file_name": img_filename,
            "width": 400,
            "height": 400
        })
        
        # 创建分割标注（圆形的多边形近似）
        polygon = []
        for angle in range(0, 360, 10):  # 每10度一个点
            x = center_x + radius * np.cos(np.radians(angle))
            y = center_y + radius * np.sin(np.radians(angle))
            polygon.extend([float(x), float(y)])
        
        reference_annotations.append({
            "id": i + 1,
            "image_id": i + 1,
            "category_id": 1,
            "bbox": [
                float(center_x - radius), float(center_y - radius),
                float(2 * radius), float(2 * radius)
            ],
            "segmentation": [polygon],
            "area": float(np.pi * radius * radius),
            "iscrowd": 0
        })
    
    # 创建目标图像（待标注）
    target_images = []
    
    for i in range(3):  # 3张待标注图像
        # 创建图像：不同背景 + 红色圆形
        img = Image.new('RGB', (400, 400), 'lightblue')
        draw = ImageDraw.Draw(img)
        
        # 不同位置和大小的圆形
        center_x = 150 + i * 30
        center_y = 200 + i * 20
        radius = 40 + i * 10
        
        draw.ellipse([
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ], fill='red')
        
        # 保存图像
        img_filename = f"target_circle_{i:03d}.jpg"
        img.save(images_dir / img_filename)
        
        target_images.append({
            "id": i + 6,  # 继续编号
            "file_name": img_filename,
            "width": 400,
            "height": 400
        })
    
    # 创建COCO格式的标注文件
    categories = [{
        "id": 1,
        "name": "red_circle",
        "supercategory": "shape"
    }]
    
    # 参考数据标注文件
    reference_coco = {
        "images": reference_images,
        "annotations": reference_annotations,
        "categories": categories
    }
    
    with open(annotations_dir / "references.json", 'w') as f:
        json.dump(reference_coco, f, indent=2)
    
    # 目标数据标注文件（只有图像信息，无标注）
    target_coco = {
        "images": target_images,
        "annotations": [],  # 空的，待预测
        "categories": categories
    }
    
    with open(annotations_dir / "targets.json", 'w') as f:
        json.dump(target_coco, f, indent=2)
    
    print(f"✅ 演示数据集创建完成:")
    print(f"   参考图像: {len(reference_images)}张")
    print(f"   目标图像: {len(target_images)}张")
    print(f"   数据目录: {demo_dir}")
    
    return demo_dir

def create_memory_pkl(demo_dir):
    """创建记忆库数据"""
    print("🧠 创建记忆库数据...")
    
    # 读取参考标注
    with open(demo_dir / "annotations" / "references.json", 'r') as f:
        coco_data = json.load(f)
    
    # 转换为记忆库格式
    memory_data = {}
    
    for ann in coco_data['annotations']:
        cat_id = ann['category_id']
        if cat_id not in memory_data:
            memory_data[cat_id] = []
        
        memory_data[cat_id].append({
            'img_id': ann['image_id'],
            'ann_ids': [ann['id']]
        })
    
    # 保存为pickle文件
    import pickle
    memory_path = demo_dir / "memory_5shot.pkl"
    with open(memory_path, 'wb') as f:
        pickle.dump(memory_data, f)
    
    print(f"✅ 记忆库数据已保存: {memory_path}")
    return memory_path

def run_inference_pipeline(demo_dir, memory_path):
    """运行推理流程"""
    print("🚀 运行推理流程...")
    
    # 创建结果目录
    results_dir = Path("demo_results")
    results_dir.mkdir(exist_ok=True)
    
    # 配置文件路径
    config_path = "dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml"
    
    # 步骤1：填充记忆库
    print("   步骤1: 填充记忆库...")
    cmd1 = f"""
    python run_lightening.py test \\
        --config {config_path} \\
        --model.test_mode fill_memory \\
        --out_path {results_dir}/memory.ckpt \\
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \\
        --model.init_args.dataset_cfgs.fill_memory.root {demo_dir}/images \\
        --model.init_args.dataset_cfgs.fill_memory.json_file {demo_dir}/annotations/references.json \\
        --model.init_args.dataset_cfgs.fill_memory.memory_pkl {memory_path} \\
        --model.init_args.dataset_cfgs.fill_memory.memory_length 5 \\
        --trainer.logger.save_dir {results_dir}/ \\
        --trainer.devices 1
    """
    
    print("   执行命令:")
    print("  ", cmd1.strip().replace('\\\n', ' ').replace('    ', ' '))
    
    # 步骤2：后处理记忆库
    print("   步骤2: 后处理记忆库...")
    cmd2 = f"""
    python run_lightening.py test \\
        --config {config_path} \\
        --model.test_mode postprocess_memory \\
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \\
        --ckpt_path {results_dir}/memory.ckpt \\
        --out_path {results_dir}/memory_postprocessed.ckpt \\
        --trainer.devices 1
    """
    
    print("   执行命令:")
    print("  ", cmd2.strip().replace('\\\n', ' ').replace('    ', ' '))
    
    # 步骤3：推理目标图像
    print("   步骤3: 推理目标图像...")
    cmd3 = f"""
    python run_lightening.py test \\
        --config {config_path} \\
        --ckpt_path {results_dir}/memory_postprocessed.ckpt \\
        --model.init_args.test_mode test \\
        --model.init_args.model_cfg.memory_bank_cfg.length 5 \\
        --model.init_args.dataset_cfgs.test.root {demo_dir}/images \\
        --model.init_args.dataset_cfgs.test.json_file {demo_dir}/annotations/targets.json \\
        --trainer.logger.save_dir {results_dir}/ \\
        --trainer.devices 1
    """
    
    print("   执行命令:")
    print("  ", cmd3.strip().replace('\\\n', ' ').replace('    ', ' '))
    
    print()
    print("📋 要运行完整流程，请依次执行上述三个命令")
    print("⚠️  注意：每个步骤都需要等待前一步完成")
    
    return [cmd1, cmd2, cmd3]

def create_run_script(commands, demo_dir):
    """创建运行脚本"""
    print("📝 创建运行脚本...")
    
    script_content = f"""#!/bin/bash
# No-Time-to-Train 演示脚本
# 自动运行完整的推理流程

set -e  # 遇到错误时停止

echo "🎯 开始 No-Time-to-Train 演示"
echo "数据目录: {demo_dir}"
echo ""

# 激活conda环境
echo "🔧 激活conda环境..."
conda activate notrain

# 设置CPU环境
export CUDA_VISIBLE_DEVICES=""
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8

cd /root/Documents/noTTrain

echo "📊 步骤1: 填充记忆库..."
{commands[0].strip()}

echo ""
echo "🔄 步骤2: 后处理记忆库..."
{commands[1].strip()}

echo ""
echo "🎯 步骤3: 推理目标图像..."
{commands[2].strip()}

echo ""
echo "🎉 演示完成！"
echo "结果保存在: demo_results/"
"""
    
    script_path = Path("run_demo.sh")
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 添加执行权限
    os.chmod(script_path, 0o755)
    
    print(f"✅ 运行脚本已创建: {script_path}")
    print("   执行方式: ./run_demo.sh")
    
    return script_path

def main():
    """主函数"""
    print("🎯 No-Time-to-Train 简单使用示例")
    print("=" * 60)
    print()
    
    print("这个示例将演示如何:")
    print("1. 准备少量参考图像和标注")
    print("2. 使用这些参考数据自动标注新图像")
    print("3. 获得高质量的分割结果")
    print()
    
    # 创建演示数据集
    demo_dir = create_demo_dataset()
    print()
    
    # 创建记忆库数据
    memory_path = create_memory_pkl(demo_dir)
    print()
    
    # 生成推理命令
    commands = run_inference_pipeline(demo_dir, memory_path)
    print()
    
    # 创建运行脚本
    script_path = create_run_script(commands, demo_dir)
    print()
    
    print("📋 使用说明:")
    print("1. 查看生成的演示数据:")
    print(f"   ls -la {demo_dir}/images/")
    print(f"   cat {demo_dir}/annotations/references.json")
    print()
    print("2. 运行完整演示:")
    print(f"   ./{script_path}")
    print()
    print("3. 查看结果:")
    print("   ls -la demo_results/")
    print()
    print("🎉 演示准备完成！")

if __name__ == "__main__":
    main()
