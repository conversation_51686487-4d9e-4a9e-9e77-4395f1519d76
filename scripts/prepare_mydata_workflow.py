#!/usr/bin/env python3
"""
为用户的mydata数据集准备完整的no-time-to-train workflow
"""

import os
import json
import pickle
from pathlib import Path
from collections import defaultdict

def analyze_dataset():
    """分析用户数据集"""
    print("🔍 分析用户数据集...")
    
    # 检查数据结构
    val_images_dir = Path("mydata/val_images")
    test_images_dir = Path("mydata/test_images")
    val_annotations = Path("mydata/val_annotations.json")
    
    print(f"参考图像目录: {val_images_dir} ({'存在' if val_images_dir.exists() else '不存在'})")
    print(f"测试图像目录: {test_images_dir} ({'存在' if test_images_dir.exists() else '不存在'})")
    print(f"参考标注文件: {val_annotations} ({'存在' if val_annotations.exists() else '不存在'})")
    
    if not val_annotations.exists():
        print("❌ 标注文件不存在！")
        return None
    
    # 读取标注文件
    with open(val_annotations, 'r') as f:
        coco_data = json.load(f)
    
    # 统计信息
    num_images = len(coco_data['images'])
    num_annotations = len(coco_data['annotations'])
    num_categories = len(coco_data['categories'])
    
    print(f"✅ 数据集统计:")
    print(f"   参考图像数: {num_images}")
    print(f"   标注数: {num_annotations}")
    print(f"   类别数: {num_categories}")
    
    # 类别信息
    print(f"   类别列表:")
    for cat in coco_data['categories']:
        print(f"     ID {cat['id']}: {cat['name']}")
    
    # 每个类别的样本数
    cat_counts = defaultdict(int)
    for ann in coco_data['annotations']:
        cat_counts[ann['category_id']] += 1
    
    print(f"   每类样本数:")
    for cat in coco_data['categories']:
        count = cat_counts[cat['id']]
        print(f"     {cat['name']}: {count}个样本")
    
    return coco_data

def generate_memory_pkl(coco_data, samples_per_class=10):
    """生成记忆库数据"""
    print(f"🧠 生成记忆库数据 (每类{samples_per_class}个样本)...")
    
    # 按类别组织标注
    annotations_by_category = defaultdict(list)
    for ann in coco_data['annotations']:
        annotations_by_category[ann['category_id']].append(ann)
    
    # 生成记忆库数据
    memory_data = {}
    
    for cat_id, annotations in annotations_by_category.items():
        # 限制每个类别的样本数
        selected_annotations = annotations[:samples_per_class]
        
        memory_data[cat_id] = []
        for ann in selected_annotations:
            memory_data[cat_id].append({
                'img_id': ann['image_id'],
                'ann_ids': [ann['id']]
            })
        
        cat_name = next(cat['name'] for cat in coco_data['categories'] if cat['id'] == cat_id)
        print(f"   类别 {cat_name}: 选择了 {len(selected_annotations)} 个样本")
    
    # 保存记忆库数据
    memory_path = Path("mydata/memory_10shot.pkl")
    with open(memory_path, 'wb') as f:
        pickle.dump(memory_data, f)
    
    print(f"✅ 记忆库数据已保存: {memory_path}")
    return memory_path

def create_test_annotations():
    """为测试图像创建空的标注文件"""
    print("📝 创建测试数据标注文件...")
    
    test_images_dir = Path("mydata/test_images")
    
    if not test_images_dir.exists():
        print("❌ 测试图像目录不存在！")
        return None
    
    # 获取测试图像列表
    test_images = list(test_images_dir.glob("*.jpg"))
    print(f"   找到 {len(test_images)} 张测试图像")
    
    # 读取参考标注以获取类别信息
    with open("mydata/val_annotations.json", 'r') as f:
        val_coco = json.load(f)
    
    # 创建测试标注文件
    test_coco = {
        "images": [],
        "annotations": [],  # 空的，待预测
        "categories": val_coco['categories']  # 使用相同的类别
    }
    
    # 添加图像信息
    for i, img_path in enumerate(sorted(test_images)):
        # 这里我们需要获取图像尺寸，简化处理假设都是相同尺寸
        # 在实际应用中应该读取图像获取真实尺寸
        test_coco["images"].append({
            "id": i + 1,
            "file_name": img_path.name,
            "width": 640,  # 默认宽度，实际应该读取图像
            "height": 480  # 默认高度，实际应该读取图像
        })
    
    # 保存测试标注文件
    test_annotations_path = Path("mydata/test_annotations.json")
    with open(test_annotations_path, 'w') as f:
        json.dump(test_coco, f, indent=2)
    
    print(f"✅ 测试标注文件已创建: {test_annotations_path}")
    print(f"   包含 {len(test_coco['images'])} 张图像信息")
    
    return test_annotations_path

def create_workflow_scripts():
    """创建workflow执行脚本"""
    print("📜 创建workflow执行脚本...")
    
    # 创建结果目录
    results_dir = Path("mydata_results")
    results_dir.mkdir(exist_ok=True)
    
    # 配置参数
    config_path = "dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L_cpu.yaml"
    memory_length = 10
    
    # 步骤1: 填充记忆库
    cmd1 = f"""python run_lightening.py test \\
    --config {config_path} \\
    --model.test_mode fill_memory \\
    --out_path {results_dir}/memory.ckpt \\
    --model.init_args.model_cfg.memory_bank_cfg.length {memory_length} \\
    --model.init_args.dataset_cfgs.fill_memory.root mydata/val_images \\
    --model.init_args.dataset_cfgs.fill_memory.json_file mydata/val_annotations.json \\
    --model.init_args.dataset_cfgs.fill_memory.memory_pkl mydata/memory_10shot.pkl \\
    --model.init_args.dataset_cfgs.fill_memory.memory_length {memory_length} \\
    --trainer.logger.save_dir {results_dir}/ \\
    --trainer.devices 1"""
    
    # 步骤2: 后处理记忆库
    cmd2 = f"""python run_lightening.py test \\
    --config {config_path} \\
    --model.test_mode postprocess_memory \\
    --model.init_args.model_cfg.memory_bank_cfg.length {memory_length} \\
    --ckpt_path {results_dir}/memory.ckpt \\
    --out_path {results_dir}/memory_postprocessed.ckpt \\
    --trainer.devices 1"""
    
    # 步骤3: 推理测试数据
    cmd3 = f"""python run_lightening.py test \\
    --config {config_path} \\
    --ckpt_path {results_dir}/memory_postprocessed.ckpt \\
    --model.init_args.test_mode test \\
    --model.init_args.model_cfg.memory_bank_cfg.length {memory_length} \\
    --model.init_args.dataset_cfgs.test.root mydata/test_images \\
    --model.init_args.dataset_cfgs.test.json_file mydata/test_annotations.json \\
    --trainer.logger.save_dir {results_dir}/ \\
    --trainer.devices 1"""
    
    # 创建执行脚本
    script_content = f"""#!/bin/bash
# No-Time-to-Train 用户数据workflow
# 数据: mydata/
# 类别: blister, Black_spot, object, stone

set -e  # 遇到错误时停止

echo "🎯 开始 No-Time-to-Train 用户数据处理"
echo "数据目录: mydata/"
echo "结果目录: {results_dir}/"
echo ""

# 激活conda环境
echo "🔧 激活conda环境..."
conda activate notrain

# 设置CPU环境
export CUDA_VISIBLE_DEVICES=""
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8

cd /root/Documents/noTTrain

echo "📊 步骤1: 填充记忆库..."
{cmd1}

echo ""
echo "🔄 步骤2: 后处理记忆库..."
{cmd2}

echo ""
echo "🎯 步骤3: 推理测试图像..."
{cmd3}

echo ""
echo "🎉 用户数据处理完成！"
echo "结果保存在: {results_dir}/"
echo ""
echo "📋 结果文件:"
echo "  - memory.ckpt: 原始记忆库"
echo "  - memory_postprocessed.ckpt: 处理后的记忆库"
echo "  - 预测结果: 在logger目录中"
"""
    
    script_path = Path("run_mydata_workflow.sh")
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 添加执行权限
    os.chmod(script_path, 0o755)
    
    print(f"✅ Workflow脚本已创建: {script_path}")
    
    return [cmd1, cmd2, cmd3], script_path

def main():
    """主函数"""
    print("🎯 用户数据 No-Time-to-Train Workflow 准备")
    print("=" * 60)
    print()
    
    # 分析数据集
    coco_data = analyze_dataset()
    if coco_data is None:
        return
    print()
    
    # 生成记忆库数据
    memory_path = generate_memory_pkl(coco_data, samples_per_class=10)
    print()
    
    # 创建测试标注文件
    test_annotations_path = create_test_annotations()
    print()
    
    # 创建workflow脚本
    commands, script_path = create_workflow_scripts()
    print()
    
    print("📋 准备完成！现在可以运行workflow:")
    print(f"   ./{script_path}")
    print()
    print("📊 数据概览:")
    print("   参考数据: mydata/val_images/ + mydata/val_annotations.json")
    print("   测试数据: mydata/test_images/")
    print("   记忆库: mydata/memory_10shot.pkl")
    print("   测试标注: mydata/test_annotations.json")
    print()
    print("🎯 预期结果:")
    print("   系统将学习参考数据中的4个类别特征")
    print("   然后自动在测试图像中检测和分割这些类别")
    print("   结果将保存在 mydata_results/ 目录中")

if __name__ == "__main__":
    main()
