seed_everything: 42

model:
  class_path: dev_hongyi.pl_wrapper.sam2matcher_pl.Sam2MatcherLightningModel
  init_args:
    model_cfg:
      name: "matching_baseline_noAMG"
      sam2_cfg_file: "sam2_hiera_l.yaml"
      sam2_ckpt_path: "./checkpoints/sam2_hiera_large.pt" # "./checkpoints/sam2_hiera_tiny.pt"
      encoder_cfg:
        name: "dinov2_large"
        img_size: 518
        patch_size: 14
      encoder_ckpt_path: "./checkpoints/dinov2/dinov2_vitl14_pretrain.pth"
      memory_bank_cfg:
        enable: True
        category_num: 20
        length: 10
        length_negative: 10
      sam2_infer_cfgs:
        points_per_side: 32
        testing_point_bs: 256
        iou_thr: 0.4
        nms_thr: 0.5
        num_out_instance: 100
        kmeans_k: 4
        n_pca_components: 3
        cls_num_per_mask: 1
        negative_score_thr: 0.2
        with_negative_refs: True
    dataset_cfgs:
      fill_memory:
        name: "coco"
        root: "./data/coco/train2017"
        json_file : "./data/coco/annotations/instances_train2017.json"
        memory_pkl: "./data/coco/annotations_refsam2/memory/train2017_allClasses_length10_v1.pkl"
        class_split: "few_shot_classes"
        image_size: 518
        norm_img: False
        context_ratio: 0.2
      support:
        name: "coco"
        root: "./data/coco/train2017"
        json_file: "./data/coco/annotations_refsam2/train2017_support_1000_v1.json"
        class_split: "few_shot_classes"
        image_size: 1024
        norm_img: False
      test:
        name: "coco"
        root: "./data/coco/val2017"
        json_file: "./data/coco/annotations/instances_val2017.json" # "./data/coco/annotations_refsam2/val2017_100.json"
        class_split: "few_shot_classes"
        image_size: 1024
        norm_img: False
    data_load_cfgs:
      workers: 4

trainer:
  devices: 4
  benchmark: true
  precision: 32
  callbacks:
    - class_path: pytorch_lightning.callbacks.TQDMProgressBar
      init_args:
        refresh_rate: 10
  logger:
    - class_path: pytorch_lightning.loggers.CSVLogger
      init_args:
        flush_logs_every_n_steps: 50
        save_dir: ./work_dirs/test_pl