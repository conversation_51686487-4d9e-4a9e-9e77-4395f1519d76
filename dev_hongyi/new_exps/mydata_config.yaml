# MyData 自定义数据集配置
# 基于 coco_fewshot_10shot_Sam2L.yaml 修改

seed_everything: 42

model:
  class_path: dev_hongyi.pl_wrapper.sam2matcher_pl.Sam2MatcherLightningModel
  init_args:
    model_cfg:
      name: "matching_baseline_noAMG"
      sam2_cfg_file: "sam2_hiera_l.yaml"
      sam2_ckpt_path: "./checkpoints/sam2_hiera_large.pt"
      sam2_infer_cfgs:
        points_per_side: 32
        testing_point_bs: 256
        iou_thr: 0.4
        nms_thr: 0.5
        num_out_instance: 100
        kmeans_k: 4
        n_pca_components: 3
        cls_num_per_mask: 1
      encoder_cfg:
        name: "dinov2_large"
        img_size: 518
        patch_size: 14
      encoder_ckpt_path: "./checkpoints/dinov2/dinov2_vitl14_pretrain.pth"
      memory_bank_cfg:
        enable: True
        category_num: 4  # blister, Black_spot, object, stone
        length: 10
      dataset_name: "mydata"
    dataset_cfgs:
      fill_memory:
        name: "coco"
        root: "./mydata/val_images"
        json_file: "./mydata/val_annotations.json"
        memory_pkl: "./mydata/memory_10shot.pkl"
        cat_names: ["blister", "Black_spot", "object", "stone"]
        image_size: 518
        memory_length: 10
        context_ratio: 0.2
        norm_img: False
      test:
        name: "mydata"
        root: "./mydata/test_images"
        json_file: "./mydata/test_annotations_3images.json"
        cat_names: ["blister", "Black_spot", "object", "stone"]
        image_size: 1024
        norm_img: False
        with_query_points: False
    data_load_cfgs:
      workers: 4

trainer:
  accelerator: gpu
  devices: 1
  precision: 16-mixed
  logger:
    class_path: pytorch_lightning.loggers.TensorBoardLogger
    init_args:
      save_dir: mydata_results/
      name: mydata_experiment
  enable_checkpointing: false
  enable_progress_bar: true
  enable_model_summary: true 