# 🚀 No-Time-to-Train Web应用

基于nottrain项目构建的用户友好Web界面，提供完整的训练免费实例分割工作流程。

## ✨ 功能特性

- 📤 **数据上传**: 支持拖拽上传参考图像和COCO格式标注文件
- 🧠 **记忆库构建**: 自动化构建和优化记忆库
- 🎯 **智能推理**: 对新图片进行分割并生成可视化结果
- 📊 **结果展示**: 实时显示检测结果和分割掩码
- 💾 **文件下载**: 支持下载JSON结果和可视化图片

## 🏗️ 系统架构

```
app/
├── main.py                 # Flask主应用
├── nottrain_wrapper.py     # NoTimeToTrain功能包装器
├── utils.py               # 工具函数（验证、可视化等）
├── templates/
│   └── index.html         # 主页面模板
├── uploads/               # 用户上传文件存储
├── results/               # 推理结果存储
└── requirements.txt       # 依赖文件
```

## 🚀 快速开始

### 1. 启动应用

```bash
# 在nottrain项目根目录执行
chmod +x start_web_app.sh
./start_web_app.sh
```

### 2. 访问界面

打开浏览器访问: `http://localhost:5000`

### 3. 使用流程

#### 步骤1: 上传参考数据
- 上传参考图像（支持多张图片）
- 上传COCO格式标注文件（必须包含分割信息）
- 系统会自动验证数据格式

#### 步骤2: 构建记忆库
- 选择记忆库大小（5-shot到30-shot）
- 点击"构建记忆库"开始训练
- 等待记忆库构建完成

#### 步骤3: 推理新图片
- 上传要分割的新图片
- 点击"开始推理"
- 查看分割结果和下载输出文件

## 📋 数据格式要求

### 参考图像
- 支持格式: JPG, PNG, BMP, TIFF
- 建议分辨率: 512x512 到 2048x2048
- 文件大小: 单张不超过50MB

### 标注文件
必须是COCO格式的JSON文件，包含以下必需字段：

```json
{
  "images": [
    {
      "id": 1,
      "file_name": "image.jpg",
      "width": 640,
      "height": 480
    }
  ],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],
      "segmentation": [[x1,y1,x2,y2,...]], // 必需！
      "area": 1234,
      "iscrowd": 0
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "object_name"
    }
  ]
}
```

⚠️ **重要**: 必须包含`segmentation`字段，仅有`bbox`是不够的！

## 🎯 推理输出

### JSON结果
```json
{
  "image_name": "test.jpg",
  "image_size": {"width": 640, "height": 480},
  "detections": [
    {
      "bbox": [x, y, width, height],
      "score": 0.85,
      "category_id": 1,
      "category_name": "object",
      "segmentation": [[x1,y1,x2,y2,...]]
    }
  ],
  "timestamp": "..."
}
```

### 可视化图片
- 带有边界框和标签的原图
- 分割掩码叠加图
- 对比显示图

## ⚙️ 配置选项

### 记忆库大小
- **5-shot**: 快速测试，适合验证流程
- **10-shot**: 推荐设置，平衡性能和速度
- **20-shot**: 高质量结果，需要更多时间
- **30-shot**: SOTA性能，最佳分割效果

### 硬件要求
- **最低**: CPU版本，8GB RAM
- **推荐**: NVIDIA GPU，12GB VRAM
- **最佳**: RTX 4090/5090，24GB VRAM

## 🔧 高级配置

### 环境变量
```bash
export CUDA_VISIBLE_DEVICES=0              # 指定GPU
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # 内存优化
export FLASK_ENV=development               # 开发模式
```

### 配置文件
默认使用`dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml`

可以通过修改`nottrain_wrapper.py`中的`config_path`参数使用自定义配置。

## 📊 性能优化

### GPU内存优化
- 减少batch size
- 使用混合精度训练（16-mixed）
- 启用梯度检查点

### 推理速度优化
- 使用较小的记忆库（5-10 shot）
- 降低输入图像分辨率
- 批量处理多张图片

## 🛠️ 故障排除

### 常见问题

**1. CUDA内存不足**
```bash
# 设置较小的内存分配
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
```

**2. 标注文件格式错误**
- 检查是否包含`segmentation`字段
- 验证JSON格式是否正确
- 确保类别ID连续且从0或1开始

**3. 模型检查点缺失**
```bash
# 下载SAM2检查点
cd checkpoints
wget https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt

# 下载DinoV2检查点
mkdir -p dinov2
cd dinov2
wget https://dl.fbaipublicfiles.com/dinov2/dinov2_vitl14/dinov2_vitl14_pretrain.pth
```

**4. 权限错误**
```bash
chmod +x start_web_app.sh
sudo chown -R $USER:$USER app/uploads app/results
```

### 日志查看
Web应用运行时会在终端显示详细日志，包括：
- 文件上传状态
- 记忆库构建进度
- 推理处理过程
- 错误信息和堆栈跟踪

## 📈 使用统计

### 项目管理
- 点击导航栏"项目管理"查看所有项目
- 显示项目状态（记忆库是否构建）
- 统计图像数量和创建时间

### 性能监控
- 记忆库构建时间
- 单张图片推理时间
- 内存和GPU使用情况

## 🔒 安全注意事项

- 上传文件大小限制：500MB
- 文件类型验证
- 项目隔离存储
- 自动清理临时文件

## 🤝 贡献和支持

如果遇到问题或有改进建议：

1. 检查控制台日志输出
2. 确认硬件和软件环境
3. 参考原项目文档
4. 提交详细的错误报告

## 📄 许可证

本Web应用基于原nottrain项目构建，遵循相同的开源许可证。

---

**享受训练免费的实例分割体验！** 🎉 