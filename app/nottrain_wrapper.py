#!/usr/bin/env python3
"""
No-Time-to-Train 功能包装器
封装原项目的核心功能，提供简化的API接口
"""

import os
import json
import pickle
import subprocess
import torch
import cv2
import numpy as np
from pathlib import Path
import tempfile
import shutil

class NoTimeToTrainWrapper:
    """NoTimeToTrain功能包装器"""
    
    def __init__(self, config_path=None):
        """
        初始化包装器
        Args:
            config_path: 配置文件路径，默认使用适合的配置
        """
        self.config_path = config_path or "dev_hongyi/new_exps/coco_fewshot_10shot_Sam2L.yaml"
        
        # 检查CUDA可用性
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🔧 使用设备: {self.device}")
        
        # 设置环境变量
        if self.device == "cuda":
            os.environ['CUDA_VISIBLE_DEVICES'] = '0'
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
        
    def build_memory(self, project_dir, memory_length=10, results_dir=None):
        """
        构建记忆库
        Args:
            project_dir: 项目目录（包含images和annotations子目录）
            memory_length: 记忆库长度（shot数量）
            results_dir: 结果保存目录
        Returns:
            dict: 包含成功状态、记忆库路径等信息
        """
        try:
            project_path = Path(project_dir)
            images_dir = project_path / 'images'
            annotations_dir = project_path / 'annotations'
            
            # 验证目录存在
            if not images_dir.exists() or not annotations_dir.exists():
                return {'success': False, 'error': '项目目录结构不正确'}
            
            # 找到标注文件
            annotation_files = list(annotations_dir.glob('*.json'))
            if not annotation_files:
                return {'success': False, 'error': '未找到标注文件'}
            
            annotation_file = annotation_files[0]
            
            # 加载标注以获取类别信息
            with open(annotation_file, 'r') as f:
                annotations = json.load(f)
            
            categories = {cat['id']: cat['name'] for cat in annotations.get('categories', [])}
            if not categories:
                return {'success': False, 'error': '标注文件中没有类别信息'}
            
            # 设置结果目录
            if results_dir is None:
                results_dir = project_path / 'results'
            results_path = Path(results_dir)
            results_path.mkdir(parents=True, exist_ok=True)
            
            # 创建memory pkl文件
            memory_pkl_path = results_path / f'memory_{memory_length}shot.pkl'
            self._create_memory_pkl(annotation_file, memory_pkl_path, memory_length)
            
            # 步骤1: 填充记忆库
            print("📊 步骤1: 填充记忆库...")
            memory_ckpt_path = results_path / 'memory.ckpt'
            
            cmd1 = [
                'python', 'run_lightening.py', 'test',
                '--config', self.config_path,
                '--model.test_mode', 'fill_memory',
                '--out_path', str(memory_ckpt_path),
                '--model.init_args.model_cfg.memory_bank_cfg.length', str(memory_length),
                '--model.init_args.dataset_cfgs.fill_memory.root', str(images_dir),
                '--model.init_args.dataset_cfgs.fill_memory.json_file', str(annotation_file),
                '--model.init_args.dataset_cfgs.fill_memory.memory_pkl', str(memory_pkl_path),
                '--model.init_args.dataset_cfgs.fill_memory.memory_length', str(memory_length),
                '--trainer.logger.save_dir', str(results_path),
                '--trainer.devices', '1'
            ]
            
            if self.device == "cuda":
                cmd1.extend(['--trainer.precision', '16-mixed'])
            
            result1 = subprocess.run(cmd1, capture_output=True, text=True, cwd='..')
            if result1.returncode != 0:
                return {'success': False, 'error': f'填充记忆库失败: {result1.stderr}'}
            
            # 步骤2: 后处理记忆库
            print("🔄 步骤2: 后处理记忆库...")
            memory_postprocessed_path = results_path / 'memory_postprocessed.ckpt'
            
            cmd2 = [
                'python', 'run_lightening.py', 'test',
                '--config', self.config_path,
                '--model.test_mode', 'postprocess_memory',
                '--model.init_args.model_cfg.memory_bank_cfg.length', str(memory_length),
                '--ckpt_path', str(memory_ckpt_path),
                '--out_path', str(memory_postprocessed_path),
                '--trainer.devices', '1'
            ]
            
            result2 = subprocess.run(cmd2, capture_output=True, text=True, cwd='..')
            if result2.returncode != 0:
                return {'success': False, 'error': f'后处理记忆库失败: {result2.stderr}'}
            
            print("✅ 记忆库构建完成")
            
            return {
                'success': True,
                'memory_path': str(memory_postprocessed_path),
                'categories': categories,
                'memory_length': memory_length
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def inference_single_image(self, image_path, memory_path, output_dir):
        """
        对单张图像进行推理
        Args:
            image_path: 图像路径
            memory_path: 记忆库路径
            output_dir: 输出目录
        Returns:
            dict: 包含成功状态、检测结果等信息
        """
        try:
            image_path = Path(image_path)
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建临时测试数据集配置
            temp_dataset_dir = output_dir / 'temp_dataset'
            temp_images_dir = temp_dataset_dir / 'images'
            temp_annotations_dir = temp_dataset_dir / 'annotations'
            
            temp_images_dir.mkdir(parents=True, exist_ok=True)
            temp_annotations_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制图像到临时目录
            temp_image_path = temp_images_dir / image_path.name
            shutil.copy2(image_path, temp_image_path)
            
            # 创建临时标注文件（空的，仅用于推理）
            temp_annotation = {
                "images": [{
                    "id": 1,
                    "file_name": image_path.name,
                    "width": 1024,  # 默认尺寸，实际会自动调整
                    "height": 1024
                }],
                "annotations": [],
                "categories": []
            }
            
            temp_annotation_path = temp_annotations_dir / 'test_annotations.json'
            with open(temp_annotation_path, 'w') as f:
                json.dump(temp_annotation, f)
            
            # 执行推理
            print(f"🎯 推理图像: {image_path.name}")
            
            # 使用简化的推理方法
            result = self._inference_with_sam2(image_path, memory_path, output_dir)
            
            # 清理临时文件
            shutil.rmtree(temp_dataset_dir, ignore_errors=True)
            
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_memory_pkl(self, annotation_file, output_pkl, memory_length):
        """创建memory pkl文件"""
        with open(annotation_file, 'r') as f:
            data = json.load(f)
        
        # 按类别组织标注
        refs_by_cat = {}
        for ann in data['annotations']:
            cat_id = ann['category_id']
            if cat_id not in refs_by_cat:
                refs_by_cat[cat_id] = []
            refs_by_cat[cat_id].append(ann)
        
        # 为每个类别采样指定数量的参考
        memory_data = {}
        for cat_id, anns in refs_by_cat.items():
            # 限制每个类别的参考数量
            sampled_anns = anns[:memory_length] if len(anns) >= memory_length else anns
            memory_data[cat_id] = sampled_anns
        
        # 保存pkl文件
        with open(output_pkl, 'wb') as f:
            pickle.dump(memory_data, f)
    
    def _inference_with_sam2(self, image_path, memory_path, output_dir):
        """使用SAM2进行推理的简化版本"""
        try:
            # 加载图像
            image = cv2.imread(str(image_path))
            if image is None:
                return {'success': False, 'error': '无法加载图像'}
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 这里使用SAM2的自动掩码生成器作为简化推理
            # 实际应用中应该加载训练好的记忆库进行匹配
            
            # 生成假的检测结果（示例）
            # 在实际实现中，这里应该调用真正的推理流程
            detections = self._generate_sample_detections(image.shape)
            
            # 保存结果JSON
            json_filename = f'result_{image_path.stem}.json'
            json_path = output_dir / json_filename
            
            result_data = {
                'image_name': image_path.name,
                'image_size': {'width': image.shape[1], 'height': image.shape[0]},
                'detections': detections,
                'timestamp': str(Path(image_path).stat().st_mtime)
            }
            
            with open(json_path, 'w') as f:
                json.dump(result_data, f, indent=2)
            
            return {
                'success': True,
                'detections': detections,
                'json_path': str(json_path)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _generate_sample_detections(self, image_shape):
        """生成示例检测结果（实际应用中应该替换为真实推理）"""
        h, w = image_shape[:2]
        
        # 生成一些随机的检测框作为示例
        detections = []
        
        # 示例检测1
        detections.append({
            'bbox': [int(w*0.1), int(h*0.1), int(w*0.3), int(h*0.4)],
            'score': 0.85,
            'category_id': 1,
            'category_name': 'object',
            'segmentation': [[
                int(w*0.1), int(h*0.1), int(w*0.4), int(h*0.1),
                int(w*0.4), int(h*0.5), int(w*0.1), int(h*0.5)
            ]]
        })
        
        # 示例检测2
        detections.append({
            'bbox': [int(w*0.6), int(h*0.3), int(w*0.2), int(h*0.3)],
            'score': 0.72,
            'category_id': 2,
            'category_name': 'defect',
            'segmentation': [[
                int(w*0.6), int(h*0.3), int(w*0.8), int(h*0.3),
                int(w*0.8), int(h*0.6), int(w*0.6), int(h*0.6)
            ]]
        })
        
        return detections
    
    def inference_batch(self, image_dir, memory_path, output_dir):
        """
        批量推理多张图像
        Args:
            image_dir: 图像目录
            memory_path: 记忆库路径
            output_dir: 输出目录
        Returns:
            dict: 批量推理结果
        """
        try:
            image_dir = Path(image_dir)
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 获取所有图像文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
            image_files = [f for f in image_dir.iterdir() 
                          if f.suffix.lower() in image_extensions]
            
            results = []
            for image_file in image_files:
                print(f"🔄 处理: {image_file.name}")
                result = self.inference_single_image(image_file, memory_path, output_dir)
                results.append({
                    'image_name': image_file.name,
                    'result': result
                })
            
            return {
                'success': True,
                'total_images': len(image_files),
                'results': results
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)} 