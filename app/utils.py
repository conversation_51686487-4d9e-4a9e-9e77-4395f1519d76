#!/usr/bin/env python3
"""
工具函数模块
包含数据验证、可视化等辅助功能
"""

import json
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import random

def validate_coco_annotations(json_path):
    """
    验证COCO格式标注文件
    Args:
        json_path: 标注文件路径
    Returns:
        tuple: (是否有效, 错误信息)
    """
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        # 检查必需的顶级字段
        required_fields = ['images', 'annotations', 'categories']
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
        
        # 检查images字段
        if not isinstance(data['images'], list):
            return False, "images字段必须是列表"
        
        for i, img in enumerate(data['images']):
            required_img_fields = ['id', 'file_name', 'width', 'height']
            for field in required_img_fields:
                if field not in img:
                    return False, f"images[{i}]缺少必需字段: {field}"
        
        # 检查categories字段
        if not isinstance(data['categories'], list):
            return False, "categories字段必须是列表"
        
        for i, cat in enumerate(data['categories']):
            required_cat_fields = ['id', 'name']
            for field in required_cat_fields:
                if field not in cat:
                    return False, f"categories[{i}]缺少必需字段: {field}"
        
        # 检查annotations字段
        if not isinstance(data['annotations'], list):
            return False, "annotations字段必须是列表"
        
        for i, ann in enumerate(data['annotations']):
            required_ann_fields = ['id', 'image_id', 'category_id', 'bbox']
            for field in required_ann_fields:
                if field not in ann:
                    return False, f"annotations[{i}]缺少必需字段: {field}"
            
            # 检查segmentation字段（关键字段）
            if 'segmentation' not in ann:
                return False, f"annotations[{i}]缺少分割信息(segmentation)字段"
            
            # 检查segmentation格式
            seg = ann['segmentation']
            if isinstance(seg, list):
                # 多边形格式
                if not seg or not isinstance(seg[0], list):
                    return False, f"annotations[{i}]的segmentation格式错误"
                
                # 检查坐标点数量（应该是偶数）
                for poly in seg:
                    if len(poly) % 2 != 0:
                        return False, f"annotations[{i}]的segmentation坐标点数量应为偶数"
            elif isinstance(seg, dict):
                # RLE格式
                if 'size' not in seg or 'counts' not in seg:
                    return False, f"annotations[{i}]的RLE格式segmentation缺少必需字段"
            else:
                return False, f"annotations[{i}]的segmentation格式不支持"
        
        return True, "标注文件格式正确"
        
    except json.JSONDecodeError:
        return False, "JSON文件格式错误"
    except Exception as e:
        return False, f"验证过程中出错: {str(e)}"

def create_visualization(image_path, detections, output_path, draw_labels=True):
    """
    创建检测结果可视化
    Args:
        image_path: 原始图像路径
        detections: 检测结果列表
        output_path: 输出图像路径
        draw_labels: 是否绘制标签
    Returns:
        str: 输出图像路径
    """
    try:
        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建matplotlib图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image_rgb)
        ax.axis('off')
        
        # 颜色列表
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta']
        
        # 绘制检测结果
        for i, detection in enumerate(detections):
            bbox = detection.get('bbox', [])
            score = detection.get('score', 0.0)
            category_name = detection.get('category_name', 'unknown')
            segmentation = detection.get('segmentation', [])
            
            color = colors[i % len(colors)]
            
            # 绘制边界框
            if len(bbox) == 4:
                x, y, w, h = bbox
                rect = patches.Rectangle((x, y), w, h, linewidth=2, 
                                       edgecolor=color, facecolor='none')
                ax.add_patch(rect)
                
                # 绘制标签
                if draw_labels:
                    label = f'{category_name}: {score:.2f}'
                    ax.text(x, y - 5, label, fontsize=10, color=color, 
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
            
            # 绘制分割轮廓
            if segmentation and isinstance(segmentation, list):
                for seg in segmentation:
                    if len(seg) >= 6:  # 至少3个点
                        points = np.array(seg).reshape(-1, 2)
                        polygon = patches.Polygon(points, linewidth=1, 
                                                edgecolor=color, facecolor=color, alpha=0.3)
                        ax.add_patch(polygon)
        
        # 添加标题
        plt.title(f'检测结果: {Path(image_path).name} (共{len(detections)}个对象)', 
                 fontsize=14, pad=20)
        
        # 保存图像
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        raise ValueError(f"创建可视化失败: {str(e)}")

def create_comparison_visualization(original_path, result_path, output_path):
    """
    创建原图与结果的对比可视化
    Args:
        original_path: 原始图像路径
        result_path: 结果图像路径
        output_path: 输出对比图像路径
    Returns:
        str: 输出对比图像路径
    """
    try:
        # 加载图像
        original = cv2.imread(str(original_path))
        result = cv2.imread(str(result_path))
        
        if original is None or result is None:
            raise ValueError("无法加载图像")
        
        # 转换颜色空间
        original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
        result_rgb = cv2.cvtColor(result, cv2.COLOR_BGR2RGB)
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        ax1.imshow(original_rgb)
        ax1.set_title('原始图像', fontsize=14)
        ax1.axis('off')
        
        ax2.imshow(result_rgb)
        ax2.set_title('检测结果', fontsize=14)
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        raise ValueError(f"创建对比可视化失败: {str(e)}")

def overlay_masks_on_image(image_path, detections, output_path, alpha=0.5):
    """
    在原图上叠加分割掩码
    Args:
        image_path: 原始图像路径
        detections: 检测结果列表
        output_path: 输出图像路径
        alpha: 透明度
    Returns:
        str: 输出图像路径
    """
    try:
        # 加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        overlay = image.copy()
        
        # 颜色列表（BGR格式）
        colors = [
            (0, 0, 255),    # 红色
            (255, 0, 0),    # 蓝色
            (0, 255, 0),    # 绿色
            (0, 255, 255),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 165, 255),  # 橙色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
        ]
        
        # 绘制每个检测结果
        for i, detection in enumerate(detections):
            segmentation = detection.get('segmentation', [])
            bbox = detection.get('bbox', [])
            category_name = detection.get('category_name', 'unknown')
            score = detection.get('score', 0.0)
            
            color = colors[i % len(colors)]
            
            # 绘制分割掩码
            if segmentation and isinstance(segmentation, list):
                for seg in segmentation:
                    if len(seg) >= 6:  # 至少3个点
                        points = np.array(seg).reshape(-1, 2).astype(np.int32)
                        cv2.fillPoly(overlay, [points], color)
            
            # 绘制边界框
            if len(bbox) == 4:
                x, y, w, h = map(int, bbox)
                cv2.rectangle(overlay, (x, y), (x + w, y + h), color, 2)
                
                # 添加标签
                label = f'{category_name}: {score:.2f}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(overlay, (x, y - label_size[1] - 10), 
                            (x + label_size[0], y), color, -1)
                cv2.putText(overlay, label, (x, y - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 混合原图和覆盖层
        result = cv2.addWeighted(image, 1 - alpha, overlay, alpha, 0)
        
        # 保存结果
        cv2.imwrite(str(output_path), result)
        
        return str(output_path)
        
    except Exception as e:
        raise ValueError(f"创建掩码叠加可视化失败: {str(e)}")

def generate_summary_report(results, output_path):
    """
    生成推理结果总结报告
    Args:
        results: 推理结果列表
        output_path: 输出报告路径
    Returns:
        str: 报告文件路径
    """
    try:
        report_data = {
            'summary': {
                'total_images': len(results),
                'successful_inferences': len([r for r in results if not r.get('error')]),
                'failed_inferences': len([r for r in results if r.get('error')]),
                'total_detections': sum(len(r.get('detections', [])) for r in results if not r.get('error'))
            },
            'detailed_results': []
        }
        
        # 按类别统计
        category_stats = {}
        
        for result in results:
            if result.get('error'):
                report_data['detailed_results'].append({
                    'image_name': result['image_name'],
                    'status': 'failed',
                    'error': result['error']
                })
            else:
                detections = result.get('detections', [])
                image_stats = {
                    'image_name': result['image_name'],
                    'status': 'success',
                    'detection_count': len(detections),
                    'categories': []
                }
                
                for detection in detections:
                    category = detection.get('category_name', 'unknown')
                    score = detection.get('score', 0.0)
                    
                    image_stats['categories'].append({
                        'name': category,
                        'score': round(score, 3)
                    })
                    
                    # 更新类别统计
                    if category not in category_stats:
                        category_stats[category] = {'count': 0, 'total_score': 0.0}
                    category_stats[category]['count'] += 1
                    category_stats[category]['total_score'] += score
                
                report_data['detailed_results'].append(image_stats)
        
        # 计算类别平均分数
        for category, stats in category_stats.items():
            stats['average_score'] = round(stats['total_score'] / stats['count'], 3)
        
        report_data['category_statistics'] = category_stats
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return str(output_path)
        
    except Exception as e:
        raise ValueError(f"生成总结报告失败: {str(e)}")

def resize_image_for_display(image_path, max_size=800):
    """
    调整图像大小以适合显示
    Args:
        image_path: 图像路径
        max_size: 最大尺寸
    Returns:
        tuple: (调整后的图像, 缩放比例)
    """
    try:
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        h, w = image.shape[:2]
        
        # 计算缩放比例
        if max(h, w) <= max_size:
            return image, 1.0
        
        scale = max_size / max(h, w)
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        return resized, scale
        
    except Exception as e:
        raise ValueError(f"调整图像大小失败: {str(e)}")

def convert_segmentation_to_mask(segmentation, image_shape):
    """
    将分割信息转换为掩码
    Args:
        segmentation: 分割信息（多边形或RLE格式）
        image_shape: 图像形状 (height, width)
    Returns:
        numpy.ndarray: 二值掩码
    """
    try:
        h, w = image_shape[:2]
        mask = np.zeros((h, w), dtype=np.uint8)
        
        if isinstance(segmentation, list):
            # 多边形格式
            for seg in segmentation:
                if len(seg) >= 6:  # 至少3个点
                    points = np.array(seg).reshape(-1, 2).astype(np.int32)
                    cv2.fillPoly(mask, [points], 1)
        elif isinstance(segmentation, dict):
            # RLE格式 - 这里需要pycocotools来解码
            # 简化实现，返回空掩码
            pass
        
        return mask
        
    except Exception as e:
        print(f"转换分割掩码失败: {str(e)}")
        return np.zeros(image_shape[:2], dtype=np.uint8) 