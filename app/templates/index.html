<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No-Time-to-Train Web应用</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .progress-step {
            display: none;
        }
        .progress-step.active {
            display: block;
        }
        .result-card {
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .detection-item {
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 5px 0;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
        }
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .step.completed .step-number {
            background-color: #28a745;
            color: white;
        }
        .step.active .step-number {
            background-color: #007bff;
            color: white;
        }
        .step-line {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background-color: #e9ecef;
            z-index: -1;
        }
        .step.completed .step-line {
            background-color: #28a745;
        }
        .loading {
            display: none;
        }
        .loading.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain"></i> No-Time-to-Train
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showProjects()">
                    <i class="fas fa-folder"></i> 项目管理
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="container mt-4">
        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-title">上传参考数据</div>
                <div class="step-line"></div>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-title">构建记忆库</div>
                <div class="step-line"></div>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-title">推理新图片</div>
            </div>
        </div>

        <!-- 步骤1: 上传参考数据 -->
        <div class="progress-step active" id="upload-section">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-upload"></i> 步骤1: 上传参考数据集和标注文件</h3>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>参考图像</h5>
                                <div class="upload-area" id="imageUploadArea">
                                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                    <p>点击或拖拽上传参考图像</p>
                                    <p class="text-muted">支持 JPG, PNG, BMP 格式</p>
                                    <input type="file" id="imageFiles" name="images" multiple accept="image/*" style="display:none;">
                                </div>
                                <div id="imagePreview" class="mt-3"></div>
                            </div>
                            <div class="col-md-6">
                                <h5>COCO格式标注文件</h5>
                                <div class="upload-area" id="annotationUploadArea">
                                    <i class="fas fa-file-code fa-3x text-muted mb-3"></i>
                                    <p>点击或拖拽上传标注文件</p>
                                    <p class="text-muted">COCO JSON格式，必须包含分割信息</p>
                                    <input type="file" id="annotationFile" name="annotations" accept=".json" style="display:none;">
                                </div>
                                <div id="annotationPreview" class="mt-3"></div>
                            </div>
                        </div>
                        <div class="mt-4 text-center">
                            <button type="submit" class="btn btn-primary btn-lg" disabled id="uploadBtn">
                                <i class="fas fa-cloud-upload-alt"></i> 上传数据
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 步骤2: 构建记忆库 -->
        <div class="progress-step" id="memory-section">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-memory"></i> 步骤2: 构建记忆库</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>记忆库配置</h5>
                            <div class="mb-3">
                                <label for="memoryLength" class="form-label">记忆库大小 (shot数量)</label>
                                <select class="form-select" id="memoryLength">
                                    <option value="5">5-shot (快速测试)</option>
                                    <option value="10" selected>10-shot (推荐)</option>
                                    <option value="20">20-shot (高质量)</option>
                                    <option value="30">30-shot (SOTA性能)</option>
                                </select>
                                <div class="form-text">更大的记忆库能提供更好的分割效果，但需要更多计算时间</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>项目信息</h5>
                            <div id="projectInfo" class="text-muted">
                                请先完成步骤1
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-success btn-lg" id="buildMemoryBtn" disabled>
                            <i class="fas fa-cogs"></i> 构建记忆库
                        </button>
                    </div>
                    <div class="loading mt-4" id="memoryLoading">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">正在构建记忆库...</span>
                            </div>
                            <p class="mt-2">正在构建记忆库，这可能需要几分钟时间...</p>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 50%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤3: 推理新图片 -->
        <div class="progress-step" id="inference-section">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-magic"></i> 步骤3: 推理新图片</h3>
                </div>
                <div class="card-body">
                    <form id="inferenceForm" enctype="multipart/form-data">
                        <div class="upload-area" id="inferenceUploadArea">
                            <i class="fas fa-image fa-3x text-muted mb-3"></i>
                            <p>上传要进行分割的新图片</p>
                            <p class="text-muted">支持多张图片同时上传</p>
                            <input type="file" id="inferenceFiles" name="images" multiple accept="image/*" style="display:none;">
                        </div>
                        <div id="inferencePreview" class="mt-3"></div>
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-warning btn-lg" disabled id="inferenceBtn">
                                <i class="fas fa-play"></i> 开始推理
                            </button>
                        </div>
                    </form>
                    <div class="loading mt-4" id="inferenceLoading">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">正在推理...</span>
                            </div>
                            <p class="mt-2">正在分析图像并生成分割结果...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果展示区域 -->
        <div id="results-section" style="display:none;">
            <div class="card mt-4">
                <div class="card-header">
                    <h3><i class="fas fa-chart-line"></i> 推理结果</h3>
                </div>
                <div class="card-body">
                    <div id="resultsContainer"></div>
                </div>
            </div>
        </div>

        <!-- 项目管理模态框 -->
        <div class="modal fade" id="projectsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">项目管理</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="projectsList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        let currentProjectId = null;
        let currentStep = 1;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStepIndicator();
        });

        function setupEventListeners() {
            // 图像上传区域
            const imageUploadArea = document.getElementById('imageUploadArea');
            const imageFiles = document.getElementById('imageFiles');
            
            imageUploadArea.addEventListener('click', () => imageFiles.click());
            imageUploadArea.addEventListener('dragover', handleDragOver);
            imageUploadArea.addEventListener('drop', handleImageDrop);
            imageFiles.addEventListener('change', handleImageSelect);

            // 标注文件上传区域
            const annotationUploadArea = document.getElementById('annotationUploadArea');
            const annotationFile = document.getElementById('annotationFile');
            
            annotationUploadArea.addEventListener('click', () => annotationFile.click());
            annotationUploadArea.addEventListener('dragover', handleDragOver);
            annotationUploadArea.addEventListener('drop', handleAnnotationDrop);
            annotationFile.addEventListener('change', handleAnnotationSelect);

            // 推理图片上传区域
            const inferenceUploadArea = document.getElementById('inferenceUploadArea');
            const inferenceFiles = document.getElementById('inferenceFiles');
            
            inferenceUploadArea.addEventListener('click', () => inferenceFiles.click());
            inferenceUploadArea.addEventListener('dragover', handleDragOver);
            inferenceUploadArea.addEventListener('drop', handleInferenceDrop);
            inferenceFiles.addEventListener('change', handleInferenceSelect);

            // 表单提交
            document.getElementById('uploadForm').addEventListener('submit', handleUpload);
            document.getElementById('buildMemoryBtn').addEventListener('click', handleBuildMemory);
            document.getElementById('inferenceForm').addEventListener('submit', handleInference);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleImageDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            document.getElementById('imageFiles').files = files;
            handleImageSelect();
        }

        function handleAnnotationDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('annotationFile').files = files;
                handleAnnotationSelect();
            }
        }

        function handleInferenceDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            document.getElementById('inferenceFiles').files = files;
            handleInferenceSelect();
        }

        function handleImageSelect() {
            const files = document.getElementById('imageFiles').files;
            const preview = document.getElementById('imagePreview');
            
            if (files.length > 0) {
                preview.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 已选择 ${files.length} 张图像
                    </div>
                `;
                checkUploadReady();
            }
        }

        function handleAnnotationSelect() {
            const file = document.getElementById('annotationFile').files[0];
            const preview = document.getElementById('annotationPreview');
            
            if (file) {
                preview.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 已选择标注文件: ${file.name}
                    </div>
                `;
                checkUploadReady();
            }
        }

        function handleInferenceSelect() {
            const files = document.getElementById('inferenceFiles').files;
            const preview = document.getElementById('inferencePreview');
            
            if (files.length > 0) {
                let html = '<div class="row">';
                for (let i = 0; i < Math.min(files.length, 4); i++) {
                    const file = files[i];
                    const url = URL.createObjectURL(file);
                    html += `
                        <div class="col-md-3">
                            <img src="${url}" class="img-thumbnail" style="height: 100px; object-fit: cover;">
                        </div>
                    `;
                }
                if (files.length > 4) {
                    html += `<div class="col-md-12 mt-2"><small class="text-muted">还有 ${files.length - 4} 张图片...</small></div>`;
                }
                html += '</div>';
                preview.innerHTML = html;
                
                document.getElementById('inferenceBtn').disabled = false;
            }
        }

        function checkUploadReady() {
            const images = document.getElementById('imageFiles').files.length > 0;
            const annotations = document.getElementById('annotationFile').files.length > 0;
            
            document.getElementById('uploadBtn').disabled = !(images && annotations);
        }

        async function handleUpload(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const imageFiles = document.getElementById('imageFiles').files;
            const annotationFile = document.getElementById('annotationFile').files[0];
            
            // 添加图像文件
            for (let file of imageFiles) {
                formData.append('images', file);
            }
            formData.append('annotations', annotationFile);
            
            try {
                showLoading('uploadBtn', '上传中...');
                
                const response = await fetch('/api/upload_reference', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentProjectId = result.project_id;
                    showSuccess('数据上传成功！');
                    
                    // 更新项目信息
                    document.getElementById('projectInfo').innerHTML = `
                        <strong>项目ID:</strong> ${result.project_id}<br>
                        <strong>图像数量:</strong> ${result.images_count}<br>
                        <strong>标注文件:</strong> ${result.annotations_file}
                    `;
                    
                    // 进入下一步
                    nextStep();
                } else {
                    showError(result.error);
                }
            } catch (error) {
                showError('上传失败: ' + error.message);
            } finally {
                hideLoading('uploadBtn', '上传数据');
            }
        }

        async function handleBuildMemory() {
            if (!currentProjectId) {
                showError('请先完成步骤1');
                return;
            }
            
            const memoryLength = document.getElementById('memoryLength').value;
            
            try {
                showLoading('memoryLoading');
                document.getElementById('buildMemoryBtn').disabled = true;
                
                const response = await fetch('/api/build_memory', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        project_id: currentProjectId,
                        memory_length: parseInt(memoryLength)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('记忆库构建成功！');
                    nextStep();
                } else {
                    showError(result.error);
                }
            } catch (error) {
                showError('构建记忆库失败: ' + error.message);
            } finally {
                hideLoading('memoryLoading');
                document.getElementById('buildMemoryBtn').disabled = false;
            }
        }

        async function handleInference(e) {
            e.preventDefault();
            
            if (!currentProjectId) {
                showError('请先完成前面的步骤');
                return;
            }
            
            const formData = new FormData();
            const files = document.getElementById('inferenceFiles').files;
            
            for (let file of files) {
                formData.append('images', file);
            }
            formData.append('project_id', currentProjectId);
            
            try {
                showLoading('inferenceLoading');
                document.getElementById('inferenceBtn').disabled = true;
                
                const response = await fetch('/api/inference', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(`推理完成！处理了 ${result.total_images} 张图片`);
                    displayResults(result.results);
                } else {
                    showError(result.error);
                }
            } catch (error) {
                showError('推理失败: ' + error.message);
            } finally {
                hideLoading('inferenceLoading');
                document.getElementById('inferenceBtn').disabled = false;
            }
        }

        function displayResults(results) {
            const container = document.getElementById('resultsContainer');
            const section = document.getElementById('results-section');
            
            let html = '';
            
            results.forEach((result, index) => {
                if (result.error) {
                    html += `
                        <div class="result-card">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <h6 class="card-title text-danger">${result.image_name}</h6>
                                    <p class="text-danger">错误: ${result.error}</p>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="result-card">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">${result.image_name}</h6>
                                    <p>检测到 ${result.detections.length} 个对象</p>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>检测详情:</h6>
                                            ${result.detections.map(det => `
                                                <div class="detection-item">
                                                    <strong>${det.category_name}</strong> 
                                                    (置信度: ${(det.score * 100).toFixed(1)}%)
                                                </div>
                                            `).join('')}
                                        </div>
                                        <div class="col-md-6">
                                            <div class="btn-group" role="group">
                                                <a href="/api/download/${currentProjectId}/${result.json_path.split('/').pop()}" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-download"></i> 下载JSON
                                                </a>
                                                ${result.visualization_path ? `
                                                    <a href="/api/download/${currentProjectId}/${result.visualization_path.split('/').pop()}" 
                                                       class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-image"></i> 下载可视化
                                                    </a>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });
            
            container.innerHTML = html;
            section.style.display = 'block';
            
            // 滚动到结果区域
            section.scrollIntoView({ behavior: 'smooth' });
        }

        function nextStep() {
            currentStep++;
            updateStepIndicator();
            
            // 隐藏当前步骤，显示下一步
            document.querySelectorAll('.progress-step').forEach(step => {
                step.classList.remove('active');
            });
            
            if (currentStep === 2) {
                document.getElementById('memory-section').classList.add('active');
                document.getElementById('buildMemoryBtn').disabled = false;
            } else if (currentStep === 3) {
                document.getElementById('inference-section').classList.add('active');
            }
        }

        function updateStepIndicator() {
            // 重置所有步骤
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active', 'completed');
            });
            
            // 标记完成的步骤
            for (let i = 1; i < currentStep; i++) {
                document.getElementById(`step${i}`).classList.add('completed');
            }
            
            // 标记当前步骤
            if (currentStep <= 3) {
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        function showLoading(elementId, text = '') {
            const element = document.getElementById(elementId);
            if (element.classList.contains('btn')) {
                element.disabled = true;
                element.innerHTML = `<span class="spinner-border spinner-border-sm" role="status"></span> ${text}`;
            } else {
                element.classList.add('active');
            }
        }

        function hideLoading(elementId, originalText) {
            const element = document.getElementById(elementId);
            if (element.classList.contains('btn')) {
                element.disabled = false;
                element.innerHTML = originalText;
            } else {
                element.classList.remove('active');
            }
        }

        function showSuccess(message) {
            showAlert(message, 'success');
        }

        function showError(message) {
            showAlert(message, 'danger');
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alert);
            
            // 自动关闭
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        async function showProjects() {
            try {
                const response = await fetch('/api/projects');
                const result = await response.json();
                
                let html = '';
                if (result.projects && result.projects.length > 0) {
                    html = '<div class="table-responsive"><table class="table"><thead><tr><th>项目ID</th><th>图像数量</th><th>记忆库状态</th><th>创建时间</th></tr></thead><tbody>';
                    
                    result.projects.forEach(project => {
                        const memoryStatus = project.has_memory ? 
                            '<span class="badge bg-success">已构建</span>' : 
                            '<span class="badge bg-warning">未构建</span>';
                        
                        html += `
                            <tr>
                                <td><code>${project.project_id}</code></td>
                                <td>${project.image_count}</td>
                                <td>${memoryStatus}</td>
                                <td>${project.created_time}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                } else {
                    html = '<p class="text-muted">暂无项目</p>';
                }
                
                document.getElementById('projectsList').innerHTML = html;
                
                const modal = new bootstrap.Modal(document.getElementById('projectsModal'));
                modal.show();
                
            } catch (error) {
                showError('获取项目列表失败: ' + error.message);
            }
        }
    </script>
</body>
</html> 