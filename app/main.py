#!/usr/bin/env python3
"""
No-Time-to-Train Web应用主程序
提供用户友好的界面来使用nottrain的分割功能
"""

import os
import json
import uuid
import shutil
from pathlib import Path
from datetime import datetime
from flask import Flask, request, render_template, jsonify, redirect, url_for, send_file
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

# 导入nottrain相关模块
import sys
sys.path.append('.')
sys.path.append('./sam2')
sys.path.append('./dinov2')

from nottrain_wrapper import NoTimeToTrainWrapper
from utils import validate_coco_annotations, create_visualization

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['UPLOAD_FOLDER'] = 'app/uploads'
app.config['RESULTS_FOLDER'] = 'app/results'
app.secret_key = 'nottrain-web-app-secret-key'

# 允许的文件扩展名
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff'}
ALLOWED_ANNOTATION_EXTENSIONS = {'json'}

def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

# 创建必要的目录
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/upload_reference', methods=['POST'])
def upload_reference():
    """上传参考数据（图像和标注文件）"""
    try:
        # 检查是否有文件上传
        if 'images' not in request.files or 'annotations' not in request.files:
            return jsonify({'error': '请上传图像文件和标注文件'}), 400
        
        images = request.files.getlist('images')
        annotations_file = request.files['annotations']
        
        # 验证文件
        if not images or not annotations_file:
            return jsonify({'error': '请选择有效的文件'}), 400
        
        # 创建唯一的项目ID
        project_id = str(uuid.uuid4())[:8]
        project_dir = Path(app.config['UPLOAD_FOLDER']) / project_id
        images_dir = project_dir / 'images'
        annotations_dir = project_dir / 'annotations'
        
        # 创建目录
        images_dir.mkdir(parents=True, exist_ok=True)
        annotations_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存图像文件
        saved_images = []
        for image in images:
            if image and allowed_file(image.filename, ALLOWED_IMAGE_EXTENSIONS):
                filename = secure_filename(image.filename)
                image_path = images_dir / filename
                image.save(str(image_path))
                saved_images.append(filename)
        
        # 保存标注文件
        if annotations_file and allowed_file(annotations_file.filename, ALLOWED_ANNOTATION_EXTENSIONS):
            filename = secure_filename(annotations_file.filename)
            annotations_path = annotations_dir / filename
            annotations_file.save(str(annotations_path))
            
            # 验证COCO格式
            is_valid, error_msg = validate_coco_annotations(str(annotations_path))
            if not is_valid:
                # 清理上传的文件
                shutil.rmtree(project_dir)
                return jsonify({'error': f'标注文件格式错误: {error_msg}'}), 400
        else:
            return jsonify({'error': '无效的标注文件'}), 400
        
        # 返回成功响应
        return jsonify({
            'success': True,
            'project_id': project_id,
            'message': f'成功上传 {len(saved_images)} 张图像和标注文件',
            'images_count': len(saved_images),
            'annotations_file': filename
        })
        
    except RequestEntityTooLarge:
        return jsonify({'error': '文件太大，请确保文件小于500MB'}), 413
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/build_memory', methods=['POST'])
def build_memory():
    """构建记忆库"""
    try:
        data = request.get_json()
        project_id = data.get('project_id')
        memory_length = data.get('memory_length', 10)  # 默认10-shot
        
        if not project_id:
            return jsonify({'error': '缺少项目ID'}), 400
        
        # 检查项目目录是否存在
        project_dir = Path(app.config['UPLOAD_FOLDER']) / project_id
        if not project_dir.exists():
            return jsonify({'error': '项目不存在'}), 404
        
        # 初始化NoTimeToTrain包装器
        wrapper = NoTimeToTrainWrapper()
        
        # 构建记忆库
        result = wrapper.build_memory(
            project_dir=str(project_dir),
            memory_length=memory_length,
            results_dir=str(Path(app.config['RESULTS_FOLDER']) / project_id)
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': '记忆库构建完成',
                'memory_path': result['memory_path'],
                'categories': result['categories']
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'构建记忆库失败: {str(e)}'}), 500

@app.route('/api/inference', methods=['POST'])
def inference():
    """对新图片进行推理"""
    try:
        # 检查是否有文件上传
        if 'images' not in request.files:
            return jsonify({'error': '请上传要分割的图像'}), 400
        
        images = request.files.getlist('images')
        project_id = request.form.get('project_id')
        
        if not project_id:
            return jsonify({'error': '缺少项目ID'}), 400
        
        # 检查记忆库是否存在
        memory_path = Path(app.config['RESULTS_FOLDER']) / project_id / 'memory_postprocessed.ckpt'
        if not memory_path.exists():
            return jsonify({'error': '请先构建记忆库'}), 400
        
        # 创建推理结果目录
        inference_dir = Path(app.config['RESULTS_FOLDER']) / project_id / 'inference'
        inference_dir.mkdir(exist_ok=True)
        
        # 保存要推理的图像
        saved_images = []
        for image in images:
            if image and allowed_file(image.filename, ALLOWED_IMAGE_EXTENSIONS):
                filename = secure_filename(image.filename)
                image_path = inference_dir / filename
                image.save(str(image_path))
                saved_images.append(filename)
        
        # 初始化NoTimeToTrain包装器
        wrapper = NoTimeToTrainWrapper()
        
        # 执行推理
        results = []
        for image_name in saved_images:
            result = wrapper.inference_single_image(
                image_path=str(inference_dir / image_name),
                memory_path=str(memory_path),
                output_dir=str(inference_dir)
            )
            
            if result['success']:
                # 创建可视化结果
                vis_path = create_visualization(
                    image_path=str(inference_dir / image_name),
                    detections=result['detections'],
                    output_path=str(inference_dir / f'vis_{image_name}')
                )
                
                results.append({
                    'image_name': image_name,
                    'detections': result['detections'],
                    'visualization_path': vis_path,
                    'json_path': result['json_path']
                })
            else:
                results.append({
                    'image_name': image_name,
                    'error': result['error']
                })
        
        return jsonify({
            'success': True,
            'results': results,
            'total_images': len(saved_images)
        })
        
    except Exception as e:
        return jsonify({'error': f'推理失败: {str(e)}'}), 500

@app.route('/api/download/<project_id>/<filename>')
def download_file(project_id, filename):
    """下载结果文件"""
    try:
        file_path = Path(app.config['RESULTS_FOLDER']) / project_id / 'inference' / filename
        if file_path.exists():
            return send_file(str(file_path), as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/api/projects')
def list_projects():
    """列出所有项目"""
    try:
        projects = []
        upload_dir = Path(app.config['UPLOAD_FOLDER'])
        results_dir = Path(app.config['RESULTS_FOLDER'])
        
        for project_path in upload_dir.iterdir():
            if project_path.is_dir():
                project_id = project_path.name
                
                # 检查项目状态
                memory_path = results_dir / project_id / 'memory_postprocessed.ckpt'
                has_memory = memory_path.exists()
                
                # 统计文件数量
                images_dir = project_path / 'images'
                image_count = len(list(images_dir.glob('*'))) if images_dir.exists() else 0
                
                projects.append({
                    'project_id': project_id,
                    'image_count': image_count,
                    'has_memory': has_memory,
                    'created_time': datetime.fromtimestamp(project_path.stat().st_ctime).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 按创建时间排序
        projects.sort(key=lambda x: x['created_time'], reverse=True)
        
        return jsonify({'projects': projects})
        
    except Exception as e:
        return jsonify({'error': f'获取项目列表失败: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': '文件太大，请确保文件小于500MB'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动 No-Time-to-Train Web应用")
    print(f"📁 上传目录: {app.config['UPLOAD_FOLDER']}")
    print(f"📊 结果目录: {app.config['RESULTS_FOLDER']}")
    print("🌐 访问地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000) 