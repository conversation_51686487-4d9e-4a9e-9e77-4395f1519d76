#!/usr/bin/env python3
"""
简化的MyData推理脚本
直接处理图像，无需复杂的配置文件
"""

import os
import json
import pickle
import torch
import cv2
import numpy as np
from pathlib import Path
from PIL import Image
import sys

# 添加项目路径
sys.path.append('.')
sys.path.append('./sam2')
sys.path.append('./dinov2')

from sam2.build_sam import build_sam2
from sam2.sam2_image_predictor import SAM2ImagePredictor

def load_sam2_model():
    """加载SAM2模型"""
    print("🔧 加载SAM2模型...")
    
    # 配置文件和检查点路径
    model_cfg = "sam2_hiera_l.yaml"  # 相对于sam2_configs目录
    checkpoint = "checkpoints/sam2_hiera_large.pt"
    
    # 构建模型
    sam2_model = build_sam2(model_cfg, checkpoint, device="cuda")
    predictor = SAM2ImagePredictor(sam2_model)
    
    print("✅ SAM2模型加载完成")
    return predictor

def process_reference_images(predictor, ref_dir, annotations_file, output_dir):
    """处理参考图像，构建记忆库"""
    print(f"📊 处理参考图像: {ref_dir}")
    
    # 加载标注
    with open(annotations_file, 'r') as f:
        data = json.load(f)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 统计类别
    categories = {cat['id']: cat['name'] for cat in data['categories']}
    print(f"📋 发现类别: {list(categories.values())}")
    
    # 处理图像
    results = []
    
    for i, img_info in enumerate(data['images'][:10]):  # 限制为前10张图像进行测试
        img_path = os.path.join(ref_dir, img_info['file_name'])
        
        if not os.path.exists(img_path):
            print(f"⚠️  图像不存在: {img_path}")
            continue
            
        print(f"🔄 处理图像 {i+1}/10: {img_info['file_name']}")
        
        # 加载图像
        image = cv2.imread(img_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 设置图像到预测器
        predictor.set_image(image_rgb)
        
        # 获取该图像的标注
        img_anns = [ann for ann in data['annotations'] if ann['image_id'] == img_info['id']]
        
        for ann in img_anns:
            # 获取边界框
            bbox = ann['bbox']  # [x, y, width, height]
            x, y, w, h = bbox
            
            # 转换为中心点
            center_x = x + w // 2
            center_y = y + h // 2
            
            # 生成掩码
            input_point = np.array([[center_x, center_y]])
            input_label = np.array([1])
            
            try:
                masks, scores, logits = predictor.predict(
                    point_coords=input_point,
                    point_labels=input_label,
                    multimask_output=True,
                )
                
                # 选择最佳掩码
                best_mask = masks[np.argmax(scores)]
                
                # 保存结果
                result = {
                    'image_id': img_info['id'],
                    'file_name': img_info['file_name'],
                    'category_id': ann['category_id'],
                    'category_name': categories[ann['category_id']],
                    'bbox': bbox,
                    'mask_score': float(scores[np.argmax(scores)]),
                }
                results.append(result)
                
                print(f"  ✅ 类别: {categories[ann['category_id']]}, 得分: {result['mask_score']:.3f}")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                continue
    
    # 保存结果
    output_file = os.path.join(output_dir, 'reference_results.json')
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"💾 参考结果已保存: {output_file}")
    return results

def process_test_images(predictor, test_dir, reference_results, output_dir):
    """处理测试图像"""
    print(f"🎯 处理测试图像: {test_dir}")
    
    # 获取测试图像列表
    test_images = list(Path(test_dir).glob('*.jpg'))[:20]  # 限制为前20张进行测试
    
    results = []
    
    for i, img_path in enumerate(test_images):
        print(f"🔄 处理测试图像 {i+1}/{len(test_images)}: {img_path.name}")
        
        # 加载图像
        image = cv2.imread(str(img_path))
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 设置图像到预测器
        predictor.set_image(image_rgb)
        
        # 自动生成掩码
        try:
            from sam2.automatic_mask_generator import SAM2AutomaticMaskGenerator
            
            mask_generator = SAM2AutomaticMaskGenerator(
                model=predictor.model,
                points_per_side=16,
                pred_iou_thresh=0.7,
                stability_score_thresh=0.85,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
            )
            
            masks = mask_generator.generate(image_rgb)
            
            # 处理生成的掩码
            for j, mask_data in enumerate(masks[:5]):  # 限制前5个掩码
                result = {
                    'image_file': img_path.name,
                    'mask_id': j,
                    'area': mask_data['area'],
                    'stability_score': mask_data['stability_score'],
                    'predicted_iou': mask_data['predicted_iou'],
                    'bbox': mask_data['bbox'],
                }
                results.append(result)
            
            print(f"  ✅ 生成 {len(masks)} 个掩码")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue
    
    # 保存结果
    output_file = os.path.join(output_dir, 'test_results.json')
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"💾 测试结果已保存: {output_file}")
    return results

def main():
    """主函数"""
    print("🚀 MyData 简化推理流程")
    print("=" * 50)
    
    # 配置路径
    ref_images_dir = "mydata/val_images"
    test_images_dir = "mydata/test_images" 
    annotations_file = "mydata/val_annotations.json"
    output_dir = "mydata_results/simple_inference"
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)}")
    
    try:
        # 加载模型
        predictor = load_sam2_model()
        
        # 处理参考图像
        reference_results = process_reference_images(
            predictor, ref_images_dir, annotations_file, output_dir
        )
        
        # 处理测试图像
        test_results = process_test_images(
            predictor, test_images_dir, reference_results, output_dir
        )
        
        print("\n🎉 处理完成！")
        print(f"📊 统计:")
        print(f"  - 参考结果: {len(reference_results)}个")
        print(f"  - 测试结果: {len(test_results)}个")
        print(f"  - 结果目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 