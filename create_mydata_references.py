#!/usr/bin/env python3
"""
为MyData数据集创建30-shot参考集
从460张参考图像中选择30张最具代表性的图像
"""

import json
import pickle
import random
import argparse
from collections import defaultdict
import os

def load_annotations(json_file):
    """加载标注文件"""
    with open(json_file, 'r') as f:
        data = json.load(f)
    return data

def create_few_shot_sampling(annotations_file, n_shot=30, seed=42, output_file=None):
    """
    创建few-shot采样
    
    Args:
        annotations_file: 标注文件路径
        n_shot: 每个类别的采样数量
        seed: 随机种子
        output_file: 输出文件路径
    """
    
    print(f"🔄 加载标注文件: {annotations_file}")
    data = load_annotations(annotations_file)
    
    # 统计类别信息
    category_to_images = defaultdict(list)
    image_id_to_info = {}
    
    # 建立图像ID到信息的映射
    for img in data['images']:
        image_id_to_info[img['id']] = img
    
    # 统计每个类别的图像
    for ann in data['annotations']:
        category_id = ann['category_id']
        image_id = ann['image_id']
        if image_id in image_id_to_info:
            category_to_images[category_id].append(image_id)
    
    # 去重
    for cat_id in category_to_images:
        category_to_images[cat_id] = list(set(category_to_images[cat_id]))
    
    print(f"📊 数据集统计:")
    print(f"  - 总图像数: {len(data['images'])}")
    print(f"  - 总标注数: {len(data['annotations'])}")
    print(f"  - 类别数: {len(category_to_images)}")
    
    for cat_id, img_ids in category_to_images.items():
        cat_name = next((cat['name'] for cat in data['categories'] if cat['id'] == cat_id), f"Category_{cat_id}")
        print(f"  - {cat_name}: {len(img_ids)}张图像")
    
    # 设置随机种子
    random.seed(seed)
    
    # 为每个类别采样
    sampled_images = []
    
    for cat_id, img_ids in category_to_images.items():
        cat_name = next((cat['name'] for cat in data['categories'] if cat['id'] == cat_id), f"Category_{cat_id}")
        
        # 如果类别图像数少于n_shot，全部使用
        if len(img_ids) <= n_shot:
            selected = img_ids
            print(f"⚠️  {cat_name}: 只有{len(img_ids)}张图像，全部使用")
        else:
            # 随机采样n_shot张
            selected = random.sample(img_ids, n_shot)
            print(f"✅ {cat_name}: 从{len(img_ids)}张中采样{len(selected)}张")
        
        # 添加图像信息
        for img_id in selected:
            img_info = image_id_to_info[img_id]
            sampled_images.append({
                'image_id': img_id,
                'file_name': img_info['file_name'],
                'category_id': cat_id,
                'category_name': cat_name
            })
    
    print(f"\n🎯 总共选择了 {len(sampled_images)} 张参考图像")
    
    # 保存结果
    if output_file is None:
        output_file = f"mydata/memory_{n_shot}shot.pkl"
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 转换为正确的记忆库格式 (按类别ID组织)
    from collections import OrderedDict
    memory_data = OrderedDict()

    # 重新加载数据以获取标注信息
    for cat_id, img_ids in category_to_images.items():
        memory_data[cat_id] = []

        # 为每个类别选择n_shot张图像
        selected_imgs = []
        for img_info in sampled_images:
            if img_info['category_id'] == cat_id:
                selected_imgs.append(img_info['image_id'])

        # 获取每张图像的标注ID
        for img_id in selected_imgs:
            # 找到该图像该类别的所有标注
            ann_ids = []
            for ann in data['annotations']:
                if ann['image_id'] == img_id and ann['category_id'] == cat_id:
                    ann_ids.append(ann['id'])

            if ann_ids:  # 只有当有标注时才添加
                memory_data[cat_id].append({
                    'img_id': img_id,
                    'ann_ids': ann_ids
                })

    # 保存为pickle格式
    with open(output_file, 'wb') as f:
        pickle.dump(memory_data, f)
    
    print(f"💾 参考集已保存到: {output_file}")
    
    return sampled_images

def main():
    parser = argparse.ArgumentParser(description='创建MyData数据集的few-shot参考集')
    parser.add_argument('--annotations', '-a', default='mydata/val_annotations.json',
                       help='标注文件路径')
    parser.add_argument('--n-shot', '-n', type=int, default=30,
                       help='每个类别的采样数量')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--output', '-o', default=None,
                       help='输出文件路径')
    
    args = parser.parse_args()
    
    print("🚀 MyData Few-Shot 参考集创建工具")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    # 创建参考集
    sampled_images = create_few_shot_sampling(
        annotations_file=args.annotations,
        n_shot=args.n_shot,
        seed=args.seed,
        output_file=args.output
    )
    
    print("\n🎉 参考集创建完成！")
    print("现在可以运行: bash run_mydata_workflow.sh")

if __name__ == "__main__":
    main() 