# pytorch_lightning==2.1.0
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: auto
  devices: '1'
  num_nodes: 1
  precision: 16-mixed
  logger:
    class_path: pytorch_lightning.loggers.TensorBoardLogger
    init_args:
      save_dir: mydata_results_30shot/
      name: mydata_experiment
      version: null
      log_graph: false
      default_hp_metric: true
      prefix: ''
      sub_dir: null
      comment: ''
      purge_step: null
      max_queue: 10
      flush_secs: 120
      filename_suffix: ''
  callbacks: null
  fast_dev_run: false
  max_epochs: null
  min_epochs: null
  max_steps: -1
  min_steps: null
  max_time: null
  limit_train_batches: null
  limit_val_batches: null
  limit_test_batches: null
  limit_predict_batches: null
  overfit_batches: 0.0
  val_check_interval: null
  check_val_every_n_epoch: 1
  num_sanity_val_steps: null
  log_every_n_steps: null
  enable_checkpointing: false
  enable_progress_bar: true
  enable_model_summary: true
  accumulate_grad_batches: 1
  gradient_clip_val: null
  gradient_clip_algorithm: null
  deterministic: null
  benchmark: null
  inference_mode: true
  use_distributed_sampler: true
  profiler: null
  detect_anomaly: false
  barebones: false
  plugins: null
  sync_batchnorm: false
  reload_dataloaders_every_n_epochs: 0
  default_root_dir: null
model:
  class_path: dev_hongyi.pl_wrapper.sam2matcher_pl.Sam2MatcherLightningModel
  init_args:
    model_cfg:
      name: matching_baseline_noAMG
      sam2_cfg_file: sam2_hiera_l.yaml
      sam2_ckpt_path: ./checkpoints/sam2_hiera_large.pt
      sam2_infer_cfgs:
        points_per_side: 32
        testing_point_bs: 256
        iou_thr: 0.4
        nms_thr: 0.5
        num_out_instance: 100
        kmeans_k: 4
        n_pca_components: 3
        cls_num_per_mask: 1
      encoder_cfg:
        name: dinov2_large
        img_size: 518
        patch_size: 14
      encoder_ckpt_path: ./checkpoints/dinov2/dinov2_vitl14_pretrain.pth
      memory_bank_cfg:
        enable: true
        category_num: 4
        length: 30
    dataset_cfgs:
      fill_memory:
        name: coco
        root: ./mydata/val_images
        json_file: ./mydata/val_annotations.json
        memory_pkl: ./mydata/memory_30shot_fixed.pkl
        cat_names:
        - blister
        - Black_spot
        - object
        - stone
        image_size: 518
        memory_length: 30
        context_ratio: 0.2
        norm_img: false
      test:
        name: coco
        root: ./mydata/test_images
        json_file: ./mydata/test_annotations.json
        cat_names:
        - blister
        - Black_spot
        - object
        - stone
        image_size: 1024
        norm_img: false
        with_query_points: false
    data_load_cfgs:
      workers: 4
    test_mode: none
data: null
out_path: null
out_support_res: null
out_neg_pkl: null
out_neg_json: null
export_result: null
seed: null
n_shot: null
coco_semantic_split: null
optimizer: null
lr_scheduler: null
ckpt_path: mydata_results_30shot/memory_postprocessed.ckpt
verbose: true
