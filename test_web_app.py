#!/usr/bin/env python3
"""
No-Time-to-Train Web应用测试脚本
用于验证应用功能是否正常工作
"""

import os
import json
import requests
import tempfile
from pathlib import Path
import numpy as np
from PIL import Image

def create_test_image(filename, size=(512, 512)):
    """创建测试图像"""
    # 创建一个简单的测试图像
    image = Image.new('RGB', size, color='white')
    
    # 添加一些简单的图形
    from PIL import ImageDraw
    draw = ImageDraw.Draw(image)
    
    # 绘制矩形
    draw.rectangle([100, 100, 200, 200], fill='red', outline='black')
    draw.rectangle([300, 200, 400, 350], fill='blue', outline='black')
    
    # 保存图像
    image.save(filename)
    return filename

def create_test_annotations():
    """创建测试COCO标注文件"""
    annotations = {
        "images": [
            {
                "id": 1,
                "file_name": "test_image_1.jpg",
                "width": 512,
                "height": 512
            },
            {
                "id": 2,
                "file_name": "test_image_2.jpg", 
                "width": 512,
                "height": 512
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 1,
                "category_id": 1,
                "bbox": [100, 100, 100, 100],
                "segmentation": [[100, 100, 200, 100, 200, 200, 100, 200]],
                "area": 10000,
                "iscrowd": 0
            },
            {
                "id": 2,
                "image_id": 2,
                "category_id": 2,
                "bbox": [300, 200, 100, 150],
                "segmentation": [[300, 200, 400, 200, 400, 350, 300, 350]],
                "area": 15000,
                "iscrowd": 0
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "red_rectangle",
                "supercategory": "shape"
            },
            {
                "id": 2,
                "name": "blue_rectangle",
                "supercategory": "shape"
            }
        ]
    }
    return annotations

def test_upload_reference_data(base_url, temp_dir):
    """测试上传参考数据"""
    print("🧪 测试上传参考数据...")
    
    # 创建测试图像
    image_files = []
    for i in range(1, 3):
        image_path = temp_dir / f"test_image_{i}.jpg"
        create_test_image(str(image_path))
        image_files.append(image_path)
    
    # 创建标注文件
    annotations = create_test_annotations()
    annotation_path = temp_dir / "annotations.json"
    with open(annotation_path, 'w') as f:
        json.dump(annotations, f, indent=2)
    
    # 准备上传数据
    files = []
    for image_path in image_files:
        files.append(('images', (image_path.name, open(image_path, 'rb'), 'image/jpeg')))
    
    files.append(('annotations', (annotation_path.name, open(annotation_path, 'rb'), 'application/json')))
    
    # 发送请求
    try:
        response = requests.post(f"{base_url}/api/upload_reference", files=files)
        
        # 关闭文件
        for _, file_tuple in files:
            if hasattr(file_tuple[1], 'close'):
                file_tuple[1].close()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 上传成功，项目ID: {result['project_id']}")
                return result['project_id']
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def test_build_memory(base_url, project_id):
    """测试构建记忆库"""
    print("🧪 测试构建记忆库...")
    
    data = {
        'project_id': project_id,
        'memory_length': 5  # 使用5-shot进行快速测试
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/build_memory",
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 记忆库构建成功")
                return True
            else:
                print(f"❌ 记忆库构建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ 构建记忆库异常: {e}")
        return False

def test_inference(base_url, project_id, temp_dir):
    """测试推理功能"""
    print("🧪 测试推理功能...")
    
    # 创建推理测试图像
    inference_image_path = temp_dir / "inference_test.jpg"
    create_test_image(str(inference_image_path), size=(640, 480))
    
    # 准备推理数据
    files = [
        ('images', (inference_image_path.name, open(inference_image_path, 'rb'), 'image/jpeg'))
    ]
    
    data = {
        'project_id': project_id
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/inference",
            files=files,
            data=data
        )
        
        # 关闭文件
        files[0][1][1].close()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 推理成功，处理了 {result['total_images']} 张图片")
                
                # 显示推理结果
                for r in result['results']:
                    if r.get('error'):
                        print(f"   ❌ {r['image_name']}: {r['error']}")
                    else:
                        print(f"   ✅ {r['image_name']}: 检测到 {len(r['detections'])} 个对象")
                        for det in r['detections']:
                            print(f"      - {det['category_name']}: {det['score']:.2f}")
                
                return True
            else:
                print(f"❌ 推理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ 推理异常: {e}")
        return False

def test_projects_list(base_url):
    """测试项目列表功能"""
    print("🧪 测试项目列表...")
    
    try:
        response = requests.get(f"{base_url}/api/projects")
        
        if response.status_code == 200:
            result = response.json()
            projects = result.get('projects', [])
            print(f"✅ 获取项目列表成功，共 {len(projects)} 个项目")
            
            for project in projects[:3]:  # 显示前3个项目
                print(f"   - {project['project_id']}: {project['image_count']} 张图片, "
                      f"记忆库: {'已构建' if project['has_memory'] else '未构建'}")
            
            return True
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取项目列表异常: {e}")
        return False

def test_app_status(base_url):
    """测试应用状态"""
    print("🧪 测试应用状态...")
    
    try:
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Web应用运行正常")
            return True
        else:
            print(f"❌ Web应用状态异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web应用，请确保应用正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试应用状态异常: {e}")
        return False

def main():
    """主测试函数"""
    base_url = "http://localhost:5000"
    
    print("🚀 No-Time-to-Train Web应用测试")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试应用状态
        if not test_app_status(base_url):
            print("\n❌ 测试失败：Web应用未运行")
            print("请先运行 ./start_web_app.sh 启动应用")
            return
        
        # 测试上传参考数据
        project_id = test_upload_reference_data(base_url, temp_path)
        if not project_id:
            print("\n❌ 测试失败：无法上传参考数据")
            return
        
        print(f"\n📋 使用项目ID: {project_id}")
        
        # 测试构建记忆库
        if not test_build_memory(base_url, project_id):
            print("\n❌ 测试失败：无法构建记忆库")
            return
        
        # 测试推理功能
        if not test_inference(base_url, project_id, temp_path):
            print("\n❌ 测试失败：推理功能异常")
            return
        
        # 测试项目列表
        if not test_projects_list(base_url):
            print("\n❌ 测试失败：无法获取项目列表")
            return
    
    print("\n🎉 所有测试通过！")
    print("Web应用功能正常，可以正常使用。")
    print(f"\n🌐 访问地址: {base_url}")

if __name__ == "__main__":
    main() 