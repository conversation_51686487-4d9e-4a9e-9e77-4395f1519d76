# 🎯 No-Time-to-Train Web应用演示指南

## 📝 概述

本指南将引导您完成No-Time-to-Train Web应用的完整使用流程，从安装到实际分割结果的获取。

## 🚀 快速启动（3步骤）

### 1️⃣ 启动Web应用

```bash
# 在nottrain项目根目录
./start_web_app.sh
```

### 2️⃣ 打开浏览器

访问: http://localhost:5000

### 3️⃣ 开始使用

按照Web界面的3个步骤完成分割任务。

## 📊 详细演示流程

### 步骤1: 上传参考数据 📤

1. **准备参考图像**
   - 选择要分割对象类型的代表性图像
   - 建议包含不同角度、光照条件的样本
   - 支持格式：JPG, PNG, BMP, TIFF

2. **准备COCO标注文件**
   ```json
   {
     "images": [...],
     "annotations": [
       {
         "segmentation": [[x1,y1,x2,y2,...]], // 必需！
         "bbox": [x,y,w,h],
         "category_id": 1,
         ...
       }
     ],
     "categories": [...]
   }
   ```

3. **上传操作**
   - 拖拽或点击上传图像文件
   - 上传对应的JSON标注文件
   - 系统自动验证格式

### 步骤2: 构建记忆库 🧠

1. **选择记忆库大小**
   - 5-shot: 快速测试 (~2分钟)
   - 10-shot: 推荐设置 (~5分钟)
   - 30-shot: 最佳性能 (~15分钟)

2. **开始构建**
   - 点击"构建记忆库"按钮
   - 等待进度条完成
   - 看到"✅记忆库构建完成"提示

### 步骤3: 推理新图片 🎯

1. **上传待分割图像**
   - 支持批量上传多张图片
   - 实时预览上传的图像

2. **开始推理**
   - 点击"开始推理"按钮
   - 查看处理进度
   - 等待分割结果

3. **查看结果**
   - 检测对象列表和置信度
   - 下载JSON结果文件
   - 下载可视化图片

## 🎨 结果展示

### JSON输出示例
```json
{
  "image_name": "test.jpg",
  "detections": [
    {
      "bbox": [100, 150, 200, 180],
      "score": 0.92,
      "category_name": "defect",
      "segmentation": [[100,150,300,150,300,330,100,330]]
    }
  ]
}
```

### 可视化效果
- 🔴 边界框标注
- 🎨 分割掩码叠加
- 🏷️ 类别标签和置信度
- 📊 检测统计信息

## 💡 使用技巧

### 🎯 获得最佳效果的建议

1. **参考数据质量**
   - 选择清晰、高质量的参考图像
   - 确保标注精确覆盖目标轮廓
   - 包含目标对象的多种状态

2. **记忆库配置**
   - 初次使用建议从10-shot开始
   - 如果效果不理想，增加到20-30shot
   - 对于复杂对象，使用更大的记忆库

3. **推理优化**
   - 新图片分辨率与参考图片接近
   - 光照和角度相似会有更好效果
   - 批量处理提高效率

## 🔧 故障排除

### 常见问题及解决方案

**Q: 上传标注文件时提示格式错误**
```
A: 检查JSON文件是否包含segmentation字段：
✅ "segmentation": [[x1,y1,x2,y2,...]]
❌ "segmentation": []
```

**Q: 记忆库构建失败**
```
A: 检查系统资源：
- GPU内存是否充足 (建议8GB+)
- 磁盘空间是否足够
- 查看终端错误日志
```

**Q: 推理结果不准确**
```
A: 优化策略：
- 增加记忆库大小 (20-30 shot)
- 检查参考数据质量
- 确保新图片与参考图片相似
```

## 📈 性能基准

### 处理时间参考（RTX 4090）

| 记忆库大小 | 构建时间 | 单张推理 | 适用场景 |
|-----------|---------|---------|----------|
| 5-shot    | ~2分钟   | ~10秒    | 快速验证 |
| 10-shot   | ~5分钟   | ~15秒    | 常规使用 |
| 20-shot   | ~10分钟  | ~20秒    | 高质量   |
| 30-shot   | ~15分钟  | ~25秒    | 最佳效果 |

### 内存使用参考

| 配置      | GPU内存 | 系统内存 | 推荐硬件 |
|----------|---------|---------|----------|
| 最低配置  | 4GB     | 8GB     | GTX 1060 |
| 推荐配置  | 8GB     | 16GB    | RTX 3080 |
| 最佳配置  | 16GB+   | 32GB+   | RTX 4090 |

## 🎮 实际应用案例

### 案例1: 工业缺陷检测
- **场景**: PCB板缺陷分割
- **数据**: 100张参考图 + 标注
- **配置**: 20-shot记忆库
- **效果**: 95%+ 检测精度

### 案例2: 医学图像分析
- **场景**: X光片病变区域分割
- **数据**: 50张参考图 + 标注
- **配置**: 30-shot记忆库
- **效果**: 临床可用精度

### 案例3: 自然图像分割
- **场景**: 动物轮廓提取
- **数据**: 30张参考图 + 标注
- **配置**: 15-shot记忆库
- **效果**: 良好的边界精度

## 📚 进阶使用

### 自定义配置
修改`app/nottrain_wrapper.py`中的配置参数：
```python
self.config_path = "your_custom_config.yaml"
```

### 批量处理脚本
使用测试脚本进行自动化处理：
```bash
python test_web_app.py
```

### API直接调用
也可以直接调用REST API：
```bash
curl -X POST http://localhost:5000/api/upload_reference \
  -F "images=@image1.jpg" \
  -F "annotations=@annotations.json"
```

## 🎯 最佳实践总结

1. **数据准备**: 高质量的参考数据是成功的关键
2. **渐进式测试**: 从小记忆库开始，逐步优化
3. **结果验证**: 结合可视化结果进行质量检查
4. **性能监控**: 关注GPU内存和处理时间
5. **错误处理**: 仔细阅读日志信息排查问题

---

🎉 **祝您使用愉快！** 

如有问题，请查看app/README.md获取更多技术细节，或检查终端日志输出。 